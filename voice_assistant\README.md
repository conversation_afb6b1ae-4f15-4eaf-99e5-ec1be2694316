# 语音助手系统 (Voice Assistant System)

一个完全离线的智能语音助手，支持中文对话和工具调用。

## ✨ 特性

- 🎤 **实时语音识别**: 基于OpenAI Whisper，支持离线中文识别
- 🧠 **智能对话**: 集成Ollama本地大语言模型，支持上下文记忆
- 🔊 **自然语音合成**: 使用Edge-TTS，支持多种中文语音
- 🎯 **智能工具调用**: 内置身份证办理、天气查询、提醒设置等工具
- 🌐 **Web界面**: 极简设计，纯语音交互体验
- 🔒 **完全离线**: 除TTS外，所有功能均可离线运行
- ⚡ **实时响应**: 端到端延迟小于5秒

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   Flask后端     │    │   Ollama API    │
│  (WebSocket)    │◄──►│   (WebSocket)   │◄──►│   (本地LLM)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   核心模块      │
                    │                 │
                    │ • ASR (Whisper) │
                    │ • TTS (Edge)    │
                    │ • VAD (WebRTC)  │
                    │ • Tools         │
                    └─────────────────┘
```

## 🚀 快速开始

### 1. 环境要求

- **Python**: 3.8 - 3.11
- **内存**: 8GB以上 (推荐16GB)
- **存储**: 20GB可用空间
- **系统**: Windows 10+, Ubuntu 18.04+, macOS 10.15+

### 2. 安装Ollama

```bash
# Linux/macOS
curl -fsSL https://ollama.ai/install.sh | sh

# Windows: 下载安装包
# https://ollama.ai/download/windows
```

### 3. 下载语言模型

```bash
# 启动Ollama服务
ollama serve

# 下载推荐的中文模型
ollama pull qwen2.5:7b
```

### 4. 安装项目

```bash
# 克隆项目
git clone <repository-url>
cd voice_assistant

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS  
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 5. 启动应用

```bash
# 方法1: 使用启动脚本 (推荐，包含环境检查)
python start.py

# 方法2: 直接启动
python run.py

# 方法3: 测试模块导入
python test_imports.py
```

### 6. 访问应用

打开浏览器访问: http://localhost:5000

## 📱 使用方法

1. **开始对话**: 按住圆形按钮说话，松开后自动处理
2. **工具调用**: 说出相关关键词触发工具，如"查询天气"、"办身份证"
3. **设置调整**: 点击右上角设置按钮调整语音参数
4. **中断播放**: 在AI回复时按住按钮可中断播放

## 🛠️ 配置说明

### 环境变量配置

创建 `.env` 文件：

```bash
# Ollama配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2.5:7b
OLLAMA_TEMPERATURE=0.7

# ASR配置
ASR_MODEL=whisper
WHISPER_MODEL_SIZE=base
ASR_LANGUAGE=zh

# TTS配置
TTS_ENGINE=edge-tts
TTS_VOICE=zh-CN-XiaoxiaoNeural

# VAD配置
VAD_AGGRESSIVENESS=2
VAD_SILENCE_THRESHOLD=1.5

# 工具配置
TOOLS_ENABLED=True
```

### 模型选择建议

| 配置类型 | Whisper模型 | Ollama模型 | 内存需求 |
|----------|-------------|------------|----------|
| 入门配置 | tiny | qwen2.5:7b-q4_0 | 4-8GB |
| 推荐配置 | base | qwen2.5:7b | 8-16GB |
| 高端配置 | small | qwen2.5:14b | 16-32GB |

## 🔧 功能模块

### 语音识别 (ASR)
- **引擎**: OpenAI Whisper
- **语言**: 中文、英文等99种语言
- **模型**: tiny/base/small/medium/large
- **特点**: 离线运行，高准确率

### 语音合成 (TTS)
- **引擎**: Edge-TTS (推荐)、pyttsx3、gTTS
- **语音**: 20+种中文语音可选
- **特点**: 自然流畅，低延迟

### 大语言模型 (LLM)
- **平台**: Ollama
- **模型**: qwen2.5、llama3.1、gemma2等
- **特点**: 本地部署，隐私安全

### 工具调用
- **身份证办理**: 流程指导和材料清单
- **天气查询**: 本地天气信息
- **提醒设置**: 语音提醒功能
- **音乐播放**: 音乐控制

## 📊 性能指标

| 指标 | 目标值 | 实际表现 |
|------|--------|----------|
| ASR延迟 | <1秒 | 0.5-1.5秒 |
| LLM响应 | <3秒 | 1-5秒 |
| TTS合成 | <2秒 | 0.5-2秒 |
| 端到端延迟 | <5秒 | 2-8秒 |
| 中文识别率 | >90% | 85-95% |

## 📁 项目结构

```
voice_assistant/
├── app.py                 # Flask主应用
├── config.py             # 配置文件
├── start.py              # 启动脚本
├── requirements.txt      # 依赖列表
├── README.md            # 项目说明
├── .env                 # 环境变量
├── static/              # 静态文件
│   ├── css/style.css    # 样式文件
│   ├── js/              # JavaScript文件
│   │   ├── app.js       # 主应用逻辑
│   │   ├── audio.js     # 音频处理
│   │   └── ui.js        # 界面管理
│   └── audio/           # 音频文件
├── templates/           # HTML模板
│   └── index.html       # 主页面
├── modules/            # 核心模块
│   ├── __init__.py     # 包初始化
│   ├── asr.py          # 语音识别
│   ├── tts.py          # 语音合成
│   ├── vad.py          # 语音活动检测
│   ├── llm.py          # 大语言模型
│   └── tools.py        # 工具调用
├── models/             # 模型文件
├── logs/               # 日志文件
└── docs/               # 文档
    ├── deployment_guide.md  # 部署指南
    ├── model_guide.md       # 模型选择指南
    └── api_reference.md     # API参考
```

## 🐛 故障排除

### 常见问题

1. **麦克风权限被拒绝**
   - 在浏览器中允许麦克风权限
   - 检查系统音频设备设置

2. **Ollama连接失败**
   - 确认Ollama服务正在运行: `ollama serve`
   - 检查端口11434是否被占用

3. **Whisper模型下载失败**
   - 检查网络连接
   - 手动下载模型文件

4. **TTS合成失败**
   - 检查网络连接 (Edge-TTS需要网络)
   - 尝试切换到pyttsx3引擎

### 性能优化

1. **减少内存占用**
   ```bash
   # 使用量化模型
   ollama pull qwen2.5:7b-q4_0
   
   # 使用更小的Whisper模型
   export WHISPER_MODEL_SIZE=tiny
   ```

2. **提升响应速度**
   ```bash
   # 启用GPU加速 (如果有NVIDIA GPU)
   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
   ```

## 📚 文档

- [部署指南](docs/deployment_guide.md) - 详细的安装和部署说明
- [模型选择指南](docs/model_guide.md) - 各种模型的选择建议
- [API参考](docs/api_reference.md) - WebSocket API文档

## 🤝 贡献

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建特性分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 提交Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [OpenAI Whisper](https://github.com/openai/whisper) - 语音识别模型
- [Ollama](https://ollama.ai/) - 本地LLM运行平台
- [Edge-TTS](https://github.com/rany2/edge-tts) - 语音合成引擎
- [Flask-SocketIO](https://flask-socketio.readthedocs.io/) - WebSocket支持

## 📞 支持

如果您遇到问题或有建议，请：

1. 查看[故障排除指南](docs/deployment_guide.md#故障排除)
2. 搜索现有的[Issues](../../issues)
3. 创建新的Issue描述问题

---

**享受与AI的语音对话吧！** 🎉
