"""
语音合成(TTS)模块
支持快速的离线中文语音合成
"""
import os
import io
import logging
import tempfile
import asyncio
import threading
import time
from typing import Optional, Union, Dict, Any, Callable
from pathlib import Path

try:
    import edge_tts
    EDGE_TTS_AVAILABLE = True
except ImportError:
    EDGE_TTS_AVAILABLE = False
    logging.warning("edge-tts not available")

try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False
    logging.warning("pyttsx3 not available")

try:
    from gtts import gTTS
    GTTS_AVAILABLE = True
except ImportError:
    GTTS_AVAILABLE = False
    logging.warning("gTTS not available")

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    logging.warning("pygame not available")

try:
    import pyaudio
    import wave
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False
    logging.warning("pyaudio not available")

try:
    from ..config import Config
except ImportError:
    # 当直接运行时，使用绝对导入
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from config import Config

logger = logging.getLogger(__name__)

class TTSEngine:
    """TTS引擎基类"""
    
    def __init__(self, config: Config):
        self.config = config
        self.voice = config.TTS_VOICE
        self.rate = config.TTS_RATE
        self.volume = config.TTS_VOLUME
        self.pitch = config.TTS_PITCH
        
    def synthesize(self, text: str, output_path: Optional[str] = None) -> Union[str, bytes]:
        """
        合成语音
        
        Args:
            text: 要合成的文本
            output_path: 输出文件路径（可选）
            
        Returns:
            Union[str, bytes]: 文件路径或音频数据
        """
        raise NotImplementedError
    
    def speak(self, text: str) -> bool:
        """
        直接播放语音
        
        Args:
            text: 要播放的文本
            
        Returns:
            bool: 是否成功
        """
        raise NotImplementedError

class EdgeTTSEngine(TTSEngine):
    """基于Edge-TTS的语音合成"""
    
    def __init__(self, config: Config):
        super().__init__(config)
        
        if not EDGE_TTS_AVAILABLE:
            raise ImportError("edge-tts is not available")
        
        self.output_format = config.TTS_OUTPUT_FORMAT
        
    async def _synthesize_async(self, text: str, output_path: str):
        """异步合成语音"""
        try:
            communicate = edge_tts.Communicate(text, self.voice, rate=self.rate, 
                                             volume=self.volume, pitch=self.pitch)
            await communicate.save(output_path)
            return True
        except Exception as e:
            logger.error(f"Edge-TTS synthesis error: {e}")
            return False
    
    def synthesize(self, text: str, output_path: Optional[str] = None) -> Union[str, bytes]:
        """合成语音"""
        try:
            if output_path is None:
                output_path = tempfile.mktemp(suffix='.mp3')
            
            # 运行异步合成
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            success = loop.run_until_complete(self._synthesize_async(text, output_path))
            loop.close()
            
            if success and os.path.exists(output_path):
                return output_path
            else:
                raise Exception("Synthesis failed")
                
        except Exception as e:
            logger.error(f"Edge-TTS synthesis error: {e}")
            return None
    
    def speak(self, text: str) -> bool:
        """直接播放语音"""
        try:
            audio_file = self.synthesize(text)
            if audio_file:
                return self._play_audio_file(audio_file)
            return False
        except Exception as e:
            logger.error(f"Edge-TTS speak error: {e}")
            return False
    
    def _play_audio_file(self, file_path: str) -> bool:
        """播放音频文件"""
        try:
            if PYGAME_AVAILABLE:
                pygame.mixer.init()
                pygame.mixer.music.load(file_path)
                pygame.mixer.music.play()
                
                # 等待播放完成
                while pygame.mixer.music.get_busy():
                    time.sleep(0.1)
                
                pygame.mixer.quit()
                return True
            else:
                # 使用系统默认播放器
                if os.name == 'nt':  # Windows
                    os.system(f'start "" "{file_path}"')
                elif os.name == 'posix':  # Linux/Mac
                    os.system(f'afplay "{file_path}" || aplay "{file_path}" || paplay "{file_path}"')
                return True
                
        except Exception as e:
            logger.error(f"Audio playback error: {e}")
            return False
        finally:
            # 清理临时文件
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
            except:
                pass

class Pyttsx3Engine(TTSEngine):
    """基于pyttsx3的语音合成"""
    
    def __init__(self, config: Config):
        super().__init__(config)
        
        if not PYTTSX3_AVAILABLE:
            raise ImportError("pyttsx3 is not available")
        
        self.engine = pyttsx3.init()
        self._configure_engine()
    
    def _configure_engine(self):
        """配置TTS引擎"""
        try:
            # 设置语音
            voices = self.engine.getProperty('voices')
            for voice in voices:
                if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                    self.engine.setProperty('voice', voice.id)
                    break
            
            # 设置语速（words per minute）
            rate = self.engine.getProperty('rate')
            self.engine.setProperty('rate', rate * 0.8)  # 稍微慢一点
            
            # 设置音量
            self.engine.setProperty('volume', 0.9)
            
        except Exception as e:
            logger.warning(f"Failed to configure pyttsx3 engine: {e}")
    
    def synthesize(self, text: str, output_path: Optional[str] = None) -> Union[str, bytes]:
        """合成语音"""
        try:
            if output_path is None:
                output_path = tempfile.mktemp(suffix='.wav')
            
            self.engine.save_to_file(text, output_path)
            self.engine.runAndWait()
            
            if os.path.exists(output_path):
                return output_path
            else:
                raise Exception("Synthesis failed")
                
        except Exception as e:
            logger.error(f"Pyttsx3 synthesis error: {e}")
            return None
    
    def speak(self, text: str) -> bool:
        """直接播放语音"""
        try:
            self.engine.say(text)
            self.engine.runAndWait()
            return True
        except Exception as e:
            logger.error(f"Pyttsx3 speak error: {e}")
            return False

class GTTSEngine(TTSEngine):
    """基于gTTS的语音合成（需要网络）"""
    
    def __init__(self, config: Config):
        super().__init__(config)
        
        if not GTTS_AVAILABLE:
            raise ImportError("gTTS is not available")
        
        self.lang = 'zh'  # 中文
    
    def synthesize(self, text: str, output_path: Optional[str] = None) -> Union[str, bytes]:
        """合成语音"""
        try:
            if output_path is None:
                output_path = tempfile.mktemp(suffix='.mp3')
            
            tts = gTTS(text=text, lang=self.lang, slow=False)
            tts.save(output_path)
            
            if os.path.exists(output_path):
                return output_path
            else:
                raise Exception("Synthesis failed")
                
        except Exception as e:
            logger.error(f"gTTS synthesis error: {e}")
            return None
    
    def speak(self, text: str) -> bool:
        """直接播放语音"""
        try:
            audio_file = self.synthesize(text)
            if audio_file:
                return self._play_audio_file(audio_file)
            return False
        except Exception as e:
            logger.error(f"gTTS speak error: {e}")
            return False
    
    def _play_audio_file(self, file_path: str) -> bool:
        """播放音频文件"""
        try:
            if PYGAME_AVAILABLE:
                pygame.mixer.init()
                pygame.mixer.music.load(file_path)
                pygame.mixer.music.play()
                
                while pygame.mixer.music.get_busy():
                    time.sleep(0.1)
                
                pygame.mixer.quit()
                return True
            return False
        except Exception as e:
            logger.error(f"Audio playback error: {e}")
            return False
        finally:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
            except:
                pass

class TTSManager:
    """TTS管理器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.engine = None
        self.is_playing = False
        self.current_audio_thread = None
        self.stop_playback = False
        
        # 回调函数
        self.playback_start_callback = None
        self.playback_end_callback = None
        self.playback_error_callback = None
        
        self._initialize_engine()
    
    def _initialize_engine(self):
        """初始化TTS引擎"""
        engine_type = self.config.TTS_ENGINE.lower()
        
        try:
            if engine_type == 'edge-tts' and EDGE_TTS_AVAILABLE:
                self.engine = EdgeTTSEngine(self.config)
                logger.info("Edge-TTS engine initialized")
            elif engine_type == 'pyttsx3' and PYTTSX3_AVAILABLE:
                self.engine = Pyttsx3Engine(self.config)
                logger.info("Pyttsx3 engine initialized")
            elif engine_type == 'gtts' and GTTS_AVAILABLE:
                self.engine = GTTSEngine(self.config)
                logger.info("gTTS engine initialized")
            else:
                # 回退到可用的引擎
                if EDGE_TTS_AVAILABLE:
                    self.engine = EdgeTTSEngine(self.config)
                    logger.info("Fallback to Edge-TTS engine")
                elif PYTTSX3_AVAILABLE:
                    self.engine = Pyttsx3Engine(self.config)
                    logger.info("Fallback to Pyttsx3 engine")
                elif GTTS_AVAILABLE:
                    self.engine = GTTSEngine(self.config)
                    logger.info("Fallback to gTTS engine")
                else:
                    raise RuntimeError("No TTS engine available")
                    
        except Exception as e:
            logger.error(f"Failed to initialize TTS engine: {e}")
            raise
    
    def synthesize(self, text: str, output_path: Optional[str] = None) -> Optional[str]:
        """
        合成语音
        
        Args:
            text: 要合成的文本
            output_path: 输出文件路径
            
        Returns:
            Optional[str]: 音频文件路径
        """
        if not text.strip():
            return None
        
        start_time = time.time()
        result = self.engine.synthesize(text, output_path)
        end_time = time.time()
        
        logger.info(f"TTS synthesis completed in {end_time - start_time:.2f}s for text: {text[:50]}...")
        return result
    
    def speak(self, text: str, blocking: bool = False) -> bool:
        """
        播放语音
        
        Args:
            text: 要播放的文本
            blocking: 是否阻塞等待播放完成
            
        Returns:
            bool: 是否成功开始播放
        """
        if not text.strip():
            return False
        
        if self.is_playing:
            self.stop_current_playback()
        
        if blocking:
            return self._speak_blocking(text)
        else:
            return self._speak_async(text)
    
    def _speak_blocking(self, text: str) -> bool:
        """阻塞式播放"""
        try:
            self.is_playing = True
            self.stop_playback = False
            
            if self.playback_start_callback:
                self.playback_start_callback(text)
            
            success = self.engine.speak(text)
            
            if self.playback_end_callback:
                self.playback_end_callback(success)
            
            return success
            
        except Exception as e:
            logger.error(f"Blocking speak error: {e}")
            if self.playback_error_callback:
                self.playback_error_callback(str(e))
            return False
        finally:
            self.is_playing = False
    
    def _speak_async(self, text: str) -> bool:
        """异步播放"""
        try:
            def speak_thread():
                self._speak_blocking(text)
            
            self.current_audio_thread = threading.Thread(target=speak_thread)
            self.current_audio_thread.daemon = True
            self.current_audio_thread.start()
            
            return True
            
        except Exception as e:
            logger.error(f"Async speak error: {e}")
            return False
    
    def stop_current_playback(self):
        """停止当前播放"""
        self.stop_playback = True
        
        if self.current_audio_thread and self.current_audio_thread.is_alive():
            # 等待线程结束
            self.current_audio_thread.join(timeout=1.0)
        
        self.is_playing = False
        logger.info("TTS playback stopped")
    
    def is_speaking(self) -> bool:
        """检查是否正在播放"""
        return self.is_playing
    
    def set_playback_callbacks(self, 
                              start_callback: Optional[Callable] = None,
                              end_callback: Optional[Callable] = None,
                              error_callback: Optional[Callable] = None):
        """设置播放回调函数"""
        self.playback_start_callback = start_callback
        self.playback_end_callback = end_callback
        self.playback_error_callback = error_callback
    
    def get_available_voices(self) -> list:
        """获取可用的语音列表"""
        if isinstance(self.engine, EdgeTTSEngine):
            # Edge-TTS支持的中文语音
            return list(self.config.TTS_VOICES.keys())
        elif isinstance(self.engine, Pyttsx3Engine):
            try:
                voices = self.engine.engine.getProperty('voices')
                return [voice.id for voice in voices]
            except:
                return []
        else:
            return ['default']
    
    def set_voice(self, voice: str):
        """设置语音"""
        self.config.TTS_VOICE = voice
        if hasattr(self.engine, 'voice'):
            self.engine.voice = voice
        logger.info(f"TTS voice set to: {voice}")

def create_tts_manager(config: Config) -> TTSManager:
    """
    创建TTS管理器
    
    Args:
        config: 配置对象
        
    Returns:
        TTSManager: TTS管理器实例
    """
    return TTSManager(config)
