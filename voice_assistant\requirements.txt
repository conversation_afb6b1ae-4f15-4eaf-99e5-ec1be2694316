# 语音助手系统依赖包

# Web框架
Flask
Flask-SocketIO
eventlet

# HTTP请求
requests
aiohttp

# 语音识别 (ASR)
openai-whisper
SpeechRecognition
pyaudio

# 语音合成 (TTS)
edge-tts
pyttsx3
gTTS

# 语音活动检测 (VAD)
webrtcvad

# 音频处理
numpy
scipy
librosa

# 音频播放
pygame
playsound

# 数据处理
pandas
python-dateutil

# 配置和环境
python-dotenv
pyyaml

# 日志和调试
colorlog

# 开发工具
pytest
pytest-asyncio
black
flake8

# 可选依赖 (根据需要安装)
# Silero VAD (高级语音检测)
silero-vad

# CUDA支持 (如果有GPU)
# torch==2.1.0+cu118 -f https://download.pytorch.org/whl/torch_stable.html
# torchaudio==2.1.0+cu118 -f https://download.pytorch.org/whl/torch_stable.html

# 系统依赖说明:
# Windows:
#   - Microsoft Visual C++ 14.0 或更高版本
#   - Windows SDK
#   - FFmpeg (用于音频处理)
#
# Linux (Ubuntu/Debian):
#   - sudo apt-get install python3-dev
#   - sudo apt-get install portaudio19-dev
#   - sudo apt-get install ffmpeg
#   - sudo apt-get install espeak espeak-data libespeak1 libespeak-dev
#   - sudo apt-get install alsa-utils
#
# macOS:
#   - brew install portaudio
#   - brew install ffmpeg
#   - brew install espeak
