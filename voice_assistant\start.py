#!/usr/bin/env python3
"""
语音助手启动脚本
检查环境、下载模型、启动服务
"""
import os
import sys
import subprocess
import platform
import time
import requests
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                        语音助手系统                          ║
    ║                    Voice Assistant System                    ║
    ║                                                              ║
    ║  一个完全离线的智能语音助手，支持中文对话和工具调用          ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    if version.major != 3 or version.minor < 8:
        print(f"❌ Python版本不支持: {version.major}.{version.minor}")
        print("   需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def check_system_dependencies():
    """检查系统依赖"""
    print("🔍 检查系统依赖...")
    
    system = platform.system()
    missing_deps = []
    
    if system == "Linux":
        # 检查Linux依赖
        deps = ["ffmpeg", "espeak", "aplay"]
        for dep in deps:
            if subprocess.run(["which", dep], capture_output=True).returncode != 0:
                missing_deps.append(dep)
    
    elif system == "Windows":
        # 检查Windows依赖
        if subprocess.run(["where", "ffmpeg"], capture_output=True, shell=True).returncode != 0:
            missing_deps.append("ffmpeg")
    
    elif system == "Darwin":  # macOS
        # 检查macOS依赖
        deps = ["ffmpeg", "espeak"]
        for dep in deps:
            if subprocess.run(["which", dep], capture_output=True).returncode != 0:
                missing_deps.append(dep)
    
    if missing_deps:
        print(f"❌ 缺少系统依赖: {', '.join(missing_deps)}")
        print("   请参考部署指南安装依赖")
        return False
    
    print("✅ 系统依赖检查通过")
    return True

def check_python_packages():
    """检查Python包"""
    print("🔍 检查Python包...")
    
    required_packages = [
        "flask", "flask_socketio", "requests", "numpy",
        "openai-whisper", "edge-tts", "webrtcvad", "torch"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少Python包: {', '.join(missing_packages)}")
        print("   正在安装缺少的包...")
        
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", 
                "-r", "requirements.txt"
            ], check=True)
            print("✅ Python包安装完成")
        except subprocess.CalledProcessError:
            print("❌ Python包安装失败")
            return False
    else:
        print("✅ Python包检查通过")
    
    return True

def check_ollama_service():
    """检查Ollama服务"""
    print("🔍 检查Ollama服务...")
    
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print(f"✅ Ollama服务运行正常，已安装 {len(models)} 个模型")
            
            # 检查推荐模型
            model_names = [model["name"] for model in models]
            recommended_models = ["qwen2.5:7b", "llama3.1:8b"]
            
            has_model = False
            for model in recommended_models:
                if any(model in name for name in model_names):
                    print(f"✅ 找到推荐模型: {model}")
                    has_model = True
                    break
            
            if not has_model:
                print("⚠️  未找到推荐的语言模型")
                print("   建议运行: ollama pull qwen2.5:7b")
            
            return True
        else:
            print("❌ Ollama服务响应异常")
            return False
            
    except requests.exceptions.RequestException:
        print("❌ 无法连接到Ollama服务")
        print("   请确保Ollama已安装并运行: ollama serve")
        return False

def download_whisper_model():
    """下载Whisper模型"""
    print("🔍 检查Whisper模型...")
    
    try:
        import whisper
        
        # 检查模型是否已存在
        model_path = Path.home() / ".cache" / "whisper"
        if model_path.exists() and any(model_path.glob("*.pt")):
            print("✅ Whisper模型已存在")
            return True
        
        print("📥 下载Whisper base模型...")
        model = whisper.load_model("base")
        print("✅ Whisper模型下载完成")
        return True
        
    except Exception as e:
        print(f"❌ Whisper模型下载失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    print("📁 创建目录结构...")
    
    directories = [
        "models",
        "logs", 
        "static/audio",
        "docs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构创建完成")

def check_environment():
    """检查环境变量"""
    print("🔍 检查环境配置...")
    
    # 创建默认.env文件
    env_file = Path(".env")
    if not env_file.exists():
        print("📝 创建默认配置文件...")
        
        default_config = """# 语音助手配置文件

# Flask配置
FLASK_ENV=development
FLASK_DEBUG=True
HOST=127.0.0.1
PORT=5000

# Ollama配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2.5:7b
OLLAMA_TIMEOUT=30
OLLAMA_TEMPERATURE=0.7

# ASR配置
ASR_MODEL=whisper
WHISPER_MODEL_SIZE=base
ASR_LANGUAGE=zh

# TTS配置
TTS_ENGINE=edge-tts
TTS_VOICE=zh-CN-XiaoxiaoNeural
TTS_RATE=+0%
TTS_VOLUME=+0%

# VAD配置
VAD_ENGINE=webrtcvad
VAD_AGGRESSIVENESS=2
VAD_SILENCE_THRESHOLD=1.5

# 工具配置
TOOLS_ENABLED=True
TOOLS_CONFIDENCE_THRESHOLD=0.8

# 日志配置
LOG_LEVEL=INFO
"""
        
        with open(env_file, "w", encoding="utf-8") as f:
            f.write(default_config)
        
        print("✅ 配置文件创建完成")
    else:
        print("✅ 配置文件已存在")

def start_application():
    """启动应用"""
    print("🚀 启动语音助手...")

    try:
        # 设置环境变量
        os.environ["PYTHONPATH"] = str(Path.cwd())

        # 使用run.py启动应用
        subprocess.run([sys.executable, "run.py"], check=True)

    except KeyboardInterrupt:
        print("\n👋 用户中断，正在关闭...")
    except subprocess.CalledProcessError as e:
        print(f"❌ 应用启动失败: {e}")
        return False

    return True

def main():
    """主函数"""
    print_banner()
    
    # 检查步骤
    checks = [
        ("Python版本", check_python_version),
        ("系统依赖", check_system_dependencies),
        ("Python包", check_python_packages),
        ("Ollama服务", check_ollama_service),
        ("Whisper模型", download_whisper_model),
    ]
    
    print("开始环境检查...\n")
    
    for name, check_func in checks:
        if not check_func():
            print(f"\n❌ {name}检查失败，请解决问题后重试")
            print("📖 详细信息请查看: docs/deployment_guide.md")
            return 1
        print()
    
    # 创建目录和配置
    create_directories()
    check_environment()
    
    print("✅ 所有检查通过！")
    print("\n" + "="*60)
    print("🎉 环境准备完成，正在启动语音助手...")
    print("📱 请在浏览器中访问: http://127.0.0.1:5000")
    print("🛑 按 Ctrl+C 停止服务")
    print("="*60 + "\n")
    
    # 等待用户确认
    try:
        input("按回车键继续启动，或按 Ctrl+C 退出...")
    except KeyboardInterrupt:
        print("\n👋 用户取消启动")
        return 0
    
    # 启动应用
    if start_application():
        print("✅ 应用正常退出")
        return 0
    else:
        print("❌ 应用异常退出")
        return 1

if __name__ == "__main__":
    sys.exit(main())
