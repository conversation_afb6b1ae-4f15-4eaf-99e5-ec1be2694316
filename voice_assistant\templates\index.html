<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音助手</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎤</text></svg>">
</head>
<body>
    <div class="container">
        <!-- 主界面 -->
        <div class="main-interface">
            <!-- 状态指示器 -->
            <div class="status-indicator" id="statusIndicator">
                <div class="status-text" id="statusText">准备就绪</div>
                <div class="status-details" id="statusDetails"></div>
            </div>
            
            <!-- 语音按钮 -->
            <div class="voice-button-container">
                <button class="voice-button" id="voiceButton" type="button">
                    <div class="button-icon" id="buttonIcon">🎤</div>
                    <div class="button-text" id="buttonText">按住说话</div>
                    <div class="recording-animation" id="recordingAnimation"></div>
                </button>
            </div>
            
            <!-- 音频可视化 -->
            <div class="audio-visualizer" id="audioVisualizer">
                <canvas id="visualizerCanvas" width="300" height="100"></canvas>
            </div>
            
            <!-- 设置按钮 -->
            <div class="settings-button" id="settingsButton">
                <span>⚙️</span>
            </div>
        </div>
        
        <!-- 工具模态框 -->
        <div class="modal" id="toolModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="toolTitle">工具</h3>
                    <span class="close" id="closeModal">&times;</span>
                </div>
                <div class="modal-body" id="toolContent">
                    <!-- 工具内容将动态加载 -->
                </div>
            </div>
        </div>
        
        <!-- 设置模态框 -->
        <div class="modal" id="settingsModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>设置</h3>
                    <span class="close" id="closeSettingsModal">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="setting-group">
                        <label for="voiceSelect">语音选择：</label>
                        <select id="voiceSelect">
                            <option value="zh-CN-XiaoxiaoNeural">晓晓（女声）</option>
                            <option value="zh-CN-YunxiNeural">云希（男声）</option>
                            <option value="zh-CN-YunyangNeural">云扬（男声）</option>
                        </select>
                    </div>
                    
                    <div class="setting-group">
                        <label for="volumeSlider">音量：</label>
                        <input type="range" id="volumeSlider" min="0" max="100" value="80">
                        <span id="volumeValue">80%</span>
                    </div>
                    
                    <div class="setting-group">
                        <label for="speedSlider">语速：</label>
                        <input type="range" id="speedSlider" min="50" max="150" value="100">
                        <span id="speedValue">100%</span>
                    </div>
                    
                    <div class="setting-group">
                        <label>
                            <input type="checkbox" id="toolsEnabled" checked>
                            启用工具调用
                        </label>
                    </div>
                    
                    <div class="setting-group">
                        <button id="clearContextButton" class="btn btn-secondary">清空对话历史</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 连接状态 -->
        <div class="connection-status" id="connectionStatus">
            <div class="connection-indicator" id="connectionIndicator"></div>
            <span id="connectionText">连接中...</span>
        </div>
        
        <!-- 错误提示 -->
        <div class="error-toast" id="errorToast">
            <div class="error-content" id="errorContent"></div>
        </div>
    </div>
    
    <!-- 音频元素 -->
    <audio id="audioPlayer" preload="auto"></audio>
    
    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="{{ url_for('static', filename='js/audio.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ui.js') }}"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
