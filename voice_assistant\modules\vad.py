"""
语音活动检测(VAD)模块
支持多种结束条件的智能语音检测功能
"""
import time
import logging
import numpy as np
from typing import Optional, Tuple, List
import threading
import queue
import re

try:
    import webrtcvad
    WEBRTCVAD_AVAILABLE = True
except ImportError:
    WEBRTCVAD_AVAILABLE = False
    logging.warning("webrtcvad not available, falling back to simple VAD")

try:
    import torch
    import torchaudio
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    logging.warning("torch/torchaudio not available, advanced VAD features disabled")

try:
    from ..config import Config
except ImportError:
    # 当直接运行时，使用绝对导入
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from config import Config

logger = logging.getLogger(__name__)

class VADDetector:
    """语音活动检测器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.sample_rate = config.AUDIO_SAMPLE_RATE
        self.frame_duration = config.VAD_FRAME_DURATION
        self.aggressiveness = config.VAD_AGGRESSIVENESS
        self.silence_threshold = config.VAD_SILENCE_THRESHOLD
        self.max_recording_time = config.VAD_MAX_RECORDING_TIME
        
        # 计算帧大小
        self.frame_size = int(self.sample_rate * self.frame_duration / 1000)
        
        # 初始化VAD引擎
        self.vad = None
        if WEBRTCVAD_AVAILABLE and config.VAD_ENGINE == 'webrtcvad':
            self.vad = webrtcvad.Vad(self.aggressiveness)
            logger.info(f"WebRTC VAD initialized with aggressiveness {self.aggressiveness}")
        else:
            logger.info("Using simple energy-based VAD")
        
        # 状态变量
        self.is_recording = False
        self.last_voice_time = 0
        self.recording_start_time = 0
        self.silence_start_time = 0
        self.voice_detected = False
        
        # 音频缓冲区
        self.audio_buffer = []
        self.frame_buffer = queue.Queue()
        
        # 语义检测相关
        self.sentence_endings = re.compile(r'[。！？.!?]')
        self.partial_text = ""
        
    def start_recording(self) -> None:
        """开始录音检测"""
        self.is_recording = True
        self.recording_start_time = time.time()
        self.last_voice_time = time.time()
        self.silence_start_time = 0
        self.voice_detected = False
        self.audio_buffer.clear()
        self.partial_text = ""
        
        # 清空帧缓冲区
        while not self.frame_buffer.empty():
            try:
                self.frame_buffer.get_nowait()
            except queue.Empty:
                break
                
        logger.info("VAD recording started")
    
    def stop_recording(self) -> None:
        """停止录音检测"""
        self.is_recording = False
        logger.info("VAD recording stopped")
    
    def process_audio_frame(self, audio_data: bytes) -> Tuple[bool, str]:
        """
        处理音频帧
        
        Args:
            audio_data: 音频数据
            
        Returns:
            Tuple[bool, str]: (是否应该停止录音, 停止原因)
        """
        if not self.is_recording:
            return False, ""
        
        current_time = time.time()
        
        # 检查最大录音时长
        if current_time - self.recording_start_time > self.max_recording_time:
            return True, "max_duration_reached"
        
        # 将音频数据添加到缓冲区
        self.audio_buffer.append(audio_data)
        
        # 检测语音活动
        is_voice = self._detect_voice_activity(audio_data)
        
        if is_voice:
            self.voice_detected = True
            self.last_voice_time = current_time
            self.silence_start_time = 0
        else:
            # 如果检测到语音后开始静音
            if self.voice_detected and self.silence_start_time == 0:
                self.silence_start_time = current_time
            
            # 检查静音持续时间
            if self.silence_start_time > 0:
                silence_duration = current_time - self.silence_start_time
                if silence_duration > self.silence_threshold:
                    return True, "silence_detected"
        
        return False, ""
    
    def _detect_voice_activity(self, audio_data: bytes) -> bool:
        """
        检测语音活动
        
        Args:
            audio_data: 音频数据
            
        Returns:
            bool: 是否检测到语音
        """
        try:
            if self.vad and len(audio_data) == self.frame_size * 2:  # 16-bit audio
                # 使用WebRTC VAD
                return self.vad.is_speech(audio_data, self.sample_rate)
            else:
                # 使用简单的能量检测
                return self._simple_voice_detection(audio_data)
        except Exception as e:
            logger.error(f"Voice activity detection error: {e}")
            return self._simple_voice_detection(audio_data)
    
    def _simple_voice_detection(self, audio_data: bytes) -> bool:
        """
        简单的基于能量的语音检测
        
        Args:
            audio_data: 音频数据
            
        Returns:
            bool: 是否检测到语音
        """
        try:
            # 转换为numpy数组
            audio_array = np.frombuffer(audio_data, dtype=np.int16)
            
            # 计算RMS能量
            rms = np.sqrt(np.mean(audio_array.astype(np.float32) ** 2))
            
            # 动态阈值（可以根据环境噪音调整）
            threshold = 500  # 可以根据实际情况调整
            
            return rms > threshold
        except Exception as e:
            logger.error(f"Simple voice detection error: {e}")
            return False
    
    def update_partial_text(self, text: str) -> bool:
        """
        更新部分识别文本，检查是否包含句子结束标志
        
        Args:
            text: 部分识别的文本
            
        Returns:
            bool: 是否检测到句子结束
        """
        self.partial_text = text
        
        # 检查是否包含句子结束标点
        if self.sentence_endings.search(text):
            logger.info(f"Sentence ending detected in: {text}")
            return True
        
        # 检查语义完整性（简单实现）
        if self._is_semantically_complete(text):
            logger.info(f"Semantically complete sentence detected: {text}")
            return True
        
        return False
    
    def _is_semantically_complete(self, text: str) -> bool:
        """
        检查语义完整性（简单实现）
        
        Args:
            text: 文本
            
        Returns:
            bool: 是否语义完整
        """
        if len(text.strip()) < 3:
            return False
        
        # 简单的语义完整性检查
        complete_patterns = [
            r'.*[是的|好的|可以|不行|没有|有的|对的|错的]$',
            r'.*[谢谢|再见|拜拜|结束].*',
            r'.*[什么|怎么|为什么|哪里|谁|何时].*[吗|呢]$',
            r'.*[请|帮我|给我].*',
        ]
        
        for pattern in complete_patterns:
            if re.search(pattern, text):
                return True
        
        return False
    
    def get_audio_data(self) -> bytes:
        """
        获取录音的音频数据
        
        Returns:
            bytes: 完整的音频数据
        """
        return b''.join(self.audio_buffer)
    
    def get_recording_duration(self) -> float:
        """
        获取录音时长
        
        Returns:
            float: 录音时长（秒）
        """
        if self.recording_start_time > 0:
            return time.time() - self.recording_start_time
        return 0.0
    
    def reset(self) -> None:
        """重置VAD状态"""
        self.is_recording = False
        self.last_voice_time = 0
        self.recording_start_time = 0
        self.silence_start_time = 0
        self.voice_detected = False
        self.audio_buffer.clear()
        self.partial_text = ""
        
        # 清空帧缓冲区
        while not self.frame_buffer.empty():
            try:
                self.frame_buffer.get_nowait()
            except queue.Empty:
                break

class AdvancedVAD:
    """高级VAD检测器（使用Silero VAD）"""
    
    def __init__(self, config: Config):
        self.config = config
        self.model = None
        self.utils = None
        
        if TORCH_AVAILABLE:
            try:
                # 尝试加载Silero VAD模型
                self.model, self.utils = torch.hub.load(
                    repo_or_dir='snakers4/silero-vad',
                    model='silero_vad',
                    force_reload=False,
                    onnx=False
                )
                logger.info("Silero VAD model loaded successfully")
            except Exception as e:
                logger.warning(f"Failed to load Silero VAD: {e}")
                self.model = None
    
    def detect_speech(self, audio_tensor: torch.Tensor, sample_rate: int = 16000) -> float:
        """
        使用Silero VAD检测语音
        
        Args:
            audio_tensor: 音频张量
            sample_rate: 采样率
            
        Returns:
            float: 语音概率 (0-1)
        """
        if self.model is None:
            return 0.5  # 默认值
        
        try:
            with torch.no_grad():
                speech_prob = self.model(audio_tensor, sample_rate).item()
            return speech_prob
        except Exception as e:
            logger.error(f"Silero VAD detection error: {e}")
            return 0.5

def create_vad_detector(config: Config) -> VADDetector:
    """
    创建VAD检测器
    
    Args:
        config: 配置对象
        
    Returns:
        VADDetector: VAD检测器实例
    """
    return VADDetector(config)
