"""
Flask后端主应用
集成WebSocket和所有核心模块
"""
import os
import sys
import logging
import json
import time
import threading
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_socketio import SocketIO, emit, disconnect
import eventlet

# 导入配置和模块
from config import get_config, Config

# 使用绝对导入避免相对导入问题
try:
    from modules.asr import create_asr_manager
    from modules.tts import create_tts_manager
    from modules.vad import create_vad_detector
    from modules.llm import create_llm_manager
    from modules.tools import create_tool_manager
except ImportError as e:
    print(f"模块导入失败: {e}")
    print("请确保所有依赖已正确安装")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('voice_assistant.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
config = get_config()
app.config.from_object(config)

# 创建SocketIO实例
socketio = SocketIO(
    app,
    cors_allowed_origins=config.SOCKETIO_CORS_ALLOWED_ORIGINS,
    async_mode=config.SOCKETIO_ASYNC_MODE,
    logger=False,
    engineio_logger=False
)

# 全局管理器实例
asr_manager = None
tts_manager = None
vad_detector = None
llm_manager = None
tool_manager = None

# 会话状态管理
session_states = {}

class SessionState:
    """会话状态类"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.is_recording = False
        self.is_processing = False
        self.is_playing = False
        self.audio_buffer = []
        self.last_activity = time.time()
        self.conversation_context = {}
        
    def reset(self):
        """重置状态"""
        self.is_recording = False
        self.is_processing = False
        self.is_playing = False
        self.audio_buffer.clear()
        self.last_activity = time.time()

def get_session_state(session_id: str) -> SessionState:
    """获取或创建会话状态"""
    if session_id not in session_states:
        session_states[session_id] = SessionState(session_id)
    session_states[session_id].last_activity = time.time()
    return session_states[session_id]

def cleanup_old_sessions():
    """清理过期会话"""
    current_time = time.time()
    expired_sessions = []
    
    for session_id, state in session_states.items():
        if current_time - state.last_activity > 3600:  # 1小时过期
            expired_sessions.append(session_id)
    
    for session_id in expired_sessions:
        del session_states[session_id]
        logger.info(f"Cleaned up expired session: {session_id}")

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/static/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    return send_from_directory('static', filename)

@app.route('/api/status')
def api_status():
    """API状态检查"""
    try:
        status = {
            'status': 'running',
            'timestamp': time.time(),
            'modules': {
                'asr': asr_manager is not None,
                'tts': tts_manager is not None,
                'vad': vad_detector is not None,
                'llm': llm_manager is not None,
                'tools': tool_manager is not None
            },
            'config': {
                'asr_model': config.ASR_MODEL,
                'tts_engine': config.TTS_ENGINE,
                'llm_model': config.OLLAMA_MODEL,
                'tools_enabled': config.TOOLS_ENABLED
            }
        }
        return jsonify(status)
    except Exception as e:
        logger.error(f"Status check error: {e}")
        return jsonify({'status': 'error', 'error': str(e)}), 500

@app.route('/api/tools')
def api_tools():
    """获取可用工具列表"""
    try:
        if tool_manager:
            tools = tool_manager.get_available_tools()
            return jsonify({'tools': tools})
        else:
            return jsonify({'tools': []})
    except Exception as e:
        logger.error(f"Tools API error: {e}")
        return jsonify({'error': str(e)}), 500

@socketio.on('connect')
def handle_connect():
    """客户端连接"""
    session_id = request.sid
    logger.info(f"Client connected: {session_id}")
    
    # 创建会话状态
    state = get_session_state(session_id)
    
    # 发送连接确认
    emit('connected', {
        'session_id': session_id,
        'timestamp': time.time(),
        'status': 'ready'
    })

@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开连接"""
    session_id = request.sid
    logger.info(f"Client disconnected: {session_id}")
    
    # 清理会话状态
    if session_id in session_states:
        state = session_states[session_id]
        state.reset()

@socketio.on('start_recording')
def handle_start_recording():
    """开始录音"""
    session_id = request.sid
    state = get_session_state(session_id)
    
    try:
        if state.is_recording or state.is_processing or state.is_playing:
            emit('error', {'message': '当前状态不允许开始录音'})
            return
        
        # 重置状态
        state.reset()
        state.is_recording = True
        state.audio_buffer.clear()
        
        # 启动VAD检测
        if vad_detector:
            vad_detector.start_recording()
        
        logger.info(f"Recording started for session: {session_id}")
        emit('recording_started', {'timestamp': time.time()})
        
    except Exception as e:
        logger.error(f"Start recording error: {e}")
        emit('error', {'message': f'开始录音失败: {str(e)}'})

@socketio.on('audio_data')
def handle_audio_data(data):
    """处理音频数据"""
    session_id = request.sid
    state = get_session_state(session_id)
    
    try:
        if not state.is_recording:
            return
        
        audio_chunk = data.get('audio', b'')
        if not audio_chunk:
            return
        
        # 添加到音频缓冲区
        state.audio_buffer.append(audio_chunk)
        
        # VAD检测
        if vad_detector:
            should_stop, reason = vad_detector.process_audio_frame(audio_chunk)
            
            if should_stop:
                logger.info(f"VAD triggered stop: {reason}")
                # 自动停止录音
                socketio.emit('auto_stop_recording', {
                    'reason': reason,
                    'timestamp': time.time()
                }, room=session_id)
                
                # 处理录音
                threading.Thread(
                    target=process_recording,
                    args=(session_id, state),
                    daemon=True
                ).start()
        
    except Exception as e:
        logger.error(f"Audio data processing error: {e}")
        emit('error', {'message': f'音频处理失败: {str(e)}'})

@socketio.on('stop_recording')
def handle_stop_recording():
    """停止录音"""
    session_id = request.sid
    state = get_session_state(session_id)
    
    try:
        if not state.is_recording:
            emit('error', {'message': '当前没有在录音'})
            return
        
        state.is_recording = False
        
        # 停止VAD检测
        if vad_detector:
            vad_detector.stop_recording()
        
        logger.info(f"Recording stopped for session: {session_id}")
        emit('recording_stopped', {'timestamp': time.time()})
        
        # 处理录音
        threading.Thread(
            target=process_recording,
            args=(session_id, state),
            daemon=True
        ).start()
        
    except Exception as e:
        logger.error(f"Stop recording error: {e}")
        emit('error', {'message': f'停止录音失败: {str(e)}'})

def process_recording(session_id: str, state: SessionState):
    """处理录音数据"""
    try:
        if not state.audio_buffer:
            socketio.emit('error', {
                'message': '没有录音数据'
            }, room=session_id)
            return
        
        state.is_processing = True
        socketio.emit('processing_started', {
            'timestamp': time.time()
        }, room=session_id)
        
        # 合并音频数据
        audio_data = b''.join(state.audio_buffer)
        logger.info(f"Processing {len(audio_data)} bytes of audio data")
        
        # 语音识别
        if asr_manager:
            asr_result = asr_manager.transcribe(audio_data)
            user_text = asr_result.get('text', '').strip()
            
            if not user_text:
                socketio.emit('processing_completed', {
                    'success': False,
                    'message': '没有识别到语音内容'
                }, room=session_id)
                state.is_processing = False
                return
            
            logger.info(f"ASR result: {user_text}")
            socketio.emit('asr_result', {
                'text': user_text,
                'confidence': asr_result.get('confidence', 0.0)
            }, room=session_id)
            
            # 检查工具调用
            tool_result = None
            if tool_manager and config.TOOLS_ENABLED:
                tool_result = tool_manager.process_user_input(
                    user_text, 
                    state.conversation_context
                )
                
                if tool_result.get('success', False):
                    logger.info(f"Tool executed: {tool_result.get('matched_tool')}")
                    socketio.emit('tool_result', tool_result, room=session_id)
            
            # LLM对话
            if llm_manager:
                # 如果有工具结果，将其添加到对话上下文
                if tool_result and tool_result.get('success', False):
                    context_text = f"用户说：{user_text}\n工具执行结果：{tool_result.get('response_text', '')}"
                else:
                    context_text = user_text
                
                llm_result = llm_manager.chat(context_text, stream=False)
                
                if llm_result.get('success', False):
                    response_text = llm_result.get('content', '')
                    logger.info(f"LLM response: {response_text}")
                    
                    socketio.emit('llm_result', {
                        'text': response_text
                    }, room=session_id)
                    
                    # 语音合成和播放
                    if tts_manager and response_text:
                        play_response(session_id, state, response_text)
                    else:
                        state.is_processing = False
                        socketio.emit('processing_completed', {
                            'success': True
                        }, room=session_id)
                else:
                    socketio.emit('error', {
                        'message': 'LLM处理失败'
                    }, room=session_id)
                    state.is_processing = False
            else:
                state.is_processing = False
                socketio.emit('processing_completed', {
                    'success': True
                }, room=session_id)
        else:
            socketio.emit('error', {
                'message': 'ASR模块未初始化'
            }, room=session_id)
            state.is_processing = False
            
    except Exception as e:
        logger.error(f"Recording processing error: {e}")
        socketio.emit('error', {
            'message': f'录音处理失败: {str(e)}'
        }, room=session_id)
        state.is_processing = False

def play_response(session_id: str, state: SessionState, text: str):
    """播放响应语音"""
    try:
        state.is_playing = True
        socketio.emit('playback_started', {
            'text': text,
            'timestamp': time.time()
        }, room=session_id)
        
        # TTS合成
        audio_file = tts_manager.synthesize(text)
        
        if audio_file:
            # 这里应该将音频文件发送给客户端播放
            # 简化实现：直接通知播放完成
            socketio.emit('playback_completed', {
                'success': True,
                'timestamp': time.time()
            }, room=session_id)
        else:
            socketio.emit('playback_completed', {
                'success': False,
                'error': 'TTS合成失败'
            }, room=session_id)
        
        state.is_playing = False
        state.is_processing = False
        
    except Exception as e:
        logger.error(f"Playback error: {e}")
        socketio.emit('error', {
            'message': f'语音播放失败: {str(e)}'
        }, room=session_id)
        state.is_playing = False
        state.is_processing = False

@socketio.on('interrupt_playback')
def handle_interrupt_playback():
    """中断播放"""
    session_id = request.sid
    state = get_session_state(session_id)
    
    try:
        if state.is_playing:
            if tts_manager:
                tts_manager.stop_current_playback()
            
            state.is_playing = False
            state.is_processing = False
            
            emit('playback_interrupted', {'timestamp': time.time()})
            logger.info(f"Playback interrupted for session: {session_id}")
        
    except Exception as e:
        logger.error(f"Interrupt playback error: {e}")
        emit('error', {'message': f'中断播放失败: {str(e)}'})

def initialize_modules():
    """初始化所有模块"""
    global asr_manager, tts_manager, vad_detector, llm_manager, tool_manager
    
    try:
        logger.info("Initializing modules...")
        
        # 初始化ASR
        asr_manager = create_asr_manager(config)
        logger.info("ASR manager initialized")
        
        # 初始化TTS
        tts_manager = create_tts_manager(config)
        logger.info("TTS manager initialized")
        
        # 初始化VAD
        vad_detector = create_vad_detector(config)
        logger.info("VAD detector initialized")
        
        # 初始化LLM
        llm_manager = create_llm_manager(config)
        logger.info("LLM manager initialized")
        
        # 初始化工具管理器
        tool_manager = create_tool_manager(config)
        logger.info("Tool manager initialized")
        
        logger.info("All modules initialized successfully")
        
    except Exception as e:
        logger.error(f"Module initialization failed: {e}")
        raise

def start_background_tasks():
    """启动后台任务"""
    def cleanup_task():
        while True:
            try:
                cleanup_old_sessions()
                time.sleep(300)  # 每5分钟清理一次
            except Exception as e:
                logger.error(f"Cleanup task error: {e}")
    
    cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
    cleanup_thread.start()
    logger.info("Background tasks started")

# 移除直接运行代码，改用run.py启动
