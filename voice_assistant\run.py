#!/usr/bin/env python3
"""
语音助手运行入口
解决Python包导入问题
"""
import os
import sys
from pathlib import Path

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['PYTHONPATH'] = str(project_root)

if __name__ == '__main__':
    try:
        # 导入并运行主应用
        from app import app, socketio, config, initialize_modules, start_background_tasks
        
        print("🚀 正在初始化语音助手...")
        
        # 初始化所有模块
        initialize_modules()
        
        # 启动后台任务
        start_background_tasks()
        
        print(f"✅ 语音助手启动成功!")
        print(f"📱 请在浏览器中访问: http://{config.HOST}:{config.PORT}")
        print(f"🛑 按 Ctrl+C 停止服务")
        print("="*60)
        
        # 启动应用
        socketio.run(
            app,
            host=config.HOST,
            port=config.PORT,
            debug=config.DEBUG,
            use_reloader=False  # 避免重复初始化
        )
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在关闭...")
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请确保已安装所有依赖: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 应用启动失败: {e}")
        sys.exit(1)
