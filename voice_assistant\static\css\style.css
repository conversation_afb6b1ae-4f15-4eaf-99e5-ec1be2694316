/* 语音助手样式文件 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Aria<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    overflow: hidden;
}

.container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

/* 主界面 */
.main-interface {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    z-index: 1;
}

/* 状态指示器 */
.status-indicator {
    text-align: center;
    margin-bottom: 1rem;
}

.status-text {
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 0.5rem;
}

.status-details {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 语音按钮容器 */
.voice-button-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 语音按钮 */
.voice-button {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(145deg, #ffffff, #e6e6e6);
    box-shadow: 
        0 10px 30px rgba(0, 0, 0, 0.2),
        inset 0 2px 10px rgba(255, 255, 255, 0.8),
        inset 0 -2px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.voice-button:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 15px 40px rgba(0, 0, 0, 0.25),
        inset 0 2px 10px rgba(255, 255, 255, 0.9),
        inset 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.voice-button:active {
    transform: translateY(0);
    box-shadow: 
        0 5px 20px rgba(0, 0, 0, 0.3),
        inset 0 2px 10px rgba(0, 0, 0, 0.1),
        inset 0 -2px 10px rgba(255, 255, 255, 0.8);
}

/* 按钮状态 */
.voice-button.recording {
    background: linear-gradient(145deg, #ff6b6b, #ee5a52);
    animation: pulse 1.5s infinite;
}

.voice-button.processing {
    background: linear-gradient(145deg, #4ecdc4, #44a08d);
    animation: spin 2s linear infinite;
}

.voice-button.playing {
    background: linear-gradient(145deg, #45b7d1, #3498db);
    animation: wave 1s ease-in-out infinite alternate;
}

.voice-button.disabled {
    background: linear-gradient(145deg, #bdc3c7, #95a5a6);
    cursor: not-allowed;
    opacity: 0.6;
}

/* 按钮图标 */
.button-icon {
    font-size: 3rem;
    line-height: 1;
    transition: all 0.3s ease;
}

.voice-button.recording .button-icon {
    color: white;
    animation: bounce 0.6s ease-in-out infinite alternate;
}

.voice-button.processing .button-icon {
    color: white;
}

.voice-button.playing .button-icon {
    color: white;
}

/* 按钮文字 */
.button-text {
    font-size: 0.9rem;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
}

.voice-button.recording .button-text,
.voice-button.processing .button-text,
.voice-button.playing .button-text {
    color: white;
}

/* 录音动画 */
.recording-animation {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    border: 3px solid rgba(255, 107, 107, 0.3);
    opacity: 0;
    animation: ripple 1.5s infinite;
}

.voice-button.recording .recording-animation {
    opacity: 1;
}

/* 音频可视化 */
.audio-visualizer {
    margin-top: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.audio-visualizer.active {
    opacity: 1;
}

#visualizerCanvas {
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

/* 设置按钮 */
.settings-button {
    position: fixed;
    top: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.5rem;
    color: white;
    z-index: 1000;
}

.settings-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 15px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 1.5rem;
}

/* 设置组 */
.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.setting-group select,
.setting-group input[type="range"] {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.setting-group input[type="checkbox"] {
    margin-right: 0.5rem;
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* 连接状态 */
.connection-status {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    color: white;
    font-size: 0.9rem;
    z-index: 1000;
}

.connection-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #ff6b6b;
    transition: background-color 0.3s ease;
}

.connection-indicator.connected {
    background: #51cf66;
}

/* 错误提示 */
.error-toast {
    position: fixed;
    top: 2rem;
    left: 50%;
    transform: translateX(-50%);
    background: #ff6b6b;
    color: white;
    padding: 1rem 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 3000;
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
    transition: all 0.3s ease;
}

.error-toast.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

/* 动画 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes wave {
    0% { transform: scale(1); }
    100% { transform: scale(1.02); }
}

@keyframes bounce {
    0% { transform: translateY(0); }
    100% { transform: translateY(-5px); }
}

@keyframes ripple {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .voice-button {
        width: 150px;
        height: 150px;
    }
    
    .button-icon {
        font-size: 2.5rem;
    }
    
    .button-text {
        font-size: 0.8rem;
    }
    
    .status-text {
        font-size: 1.2rem;
    }
    
    .settings-button {
        top: 1rem;
        right: 1rem;
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    .connection-status {
        bottom: 1rem;
        left: 1rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .voice-button {
        width: 120px;
        height: 120px;
    }
    
    .button-icon {
        font-size: 2rem;
    }
    
    .button-text {
        font-size: 0.7rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}
