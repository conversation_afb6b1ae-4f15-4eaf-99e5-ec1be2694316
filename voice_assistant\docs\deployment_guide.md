# 语音助手系统部署指南

## 系统要求

### 硬件要求
- **CPU**: 4核心以上，推荐8核心
- **内存**: 8GB以上，推荐16GB
- **存储**: 20GB可用空间（用于模型文件）
- **网络**: 稳定的网络连接（用于下载模型）
- **音频设备**: 麦克风和扬声器

### 软件要求
- **Python**: 3.8 - 3.11
- **操作系统**: Windows 10+, Ubuntu 18.04+, macOS 10.15+
- **浏览器**: Chrome 80+, Firefox 75+, Safari 13+

## 安装步骤

### 1. 环境准备

#### Windows
```bash
# 安装Python (推荐使用官方安装包)
# 下载地址: https://www.python.org/downloads/

# 安装Visual Studio Build Tools
# 下载地址: https://visualstudio.microsoft.com/visual-cpp-build-tools/

# 安装FFmpeg
# 下载地址: https://ffmpeg.org/download.html
# 将ffmpeg.exe添加到系统PATH
```

#### Ubuntu/Debian
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python和开发工具
sudo apt install python3 python3-pip python3-dev python3-venv

# 安装音频相关依赖
sudo apt install portaudio19-dev alsa-utils
sudo apt install ffmpeg espeak espeak-data libespeak1 libespeak-dev

# 安装系统库
sudo apt install build-essential cmake pkg-config
sudo apt install libsndfile1-dev libffi-dev
```

#### macOS
```bash
# 安装Homebrew (如果未安装)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装依赖
brew install python@3.10
brew install portaudio ffmpeg espeak
```

### 2. 项目部署

#### 克隆或下载项目
```bash
# 如果使用Git
git clone <repository-url>
cd voice_assistant

# 或者直接下载并解压项目文件
```

#### 创建虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate
```

#### 安装Python依赖
```bash
# 升级pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt

# 如果遇到安装问题，可以尝试分步安装
pip install Flask Flask-SocketIO eventlet requests
pip install openai-whisper SpeechRecognition
pip install edge-tts pyttsx3
pip install webrtcvad torch torchaudio
pip install numpy scipy librosa pygame
```

### 3. Ollama安装和配置

#### 安装Ollama
```bash
# Linux/macOS
curl -fsSL https://ollama.ai/install.sh | sh

# Windows
# 下载安装包: https://ollama.ai/download/windows
```

#### 下载语言模型
```bash
# 下载推荐的中文模型
ollama pull qwen2.5:7b

# 或者下载其他模型
ollama pull llama3.1:8b
ollama pull gemma2:9b

# 查看已安装的模型
ollama list
```

#### 启动Ollama服务
```bash
# 启动Ollama服务 (默认端口11434)
ollama serve

# 测试服务是否正常
curl http://localhost:11434/api/tags
```

### 4. 模型文件准备

#### Whisper模型下载
```bash
# 进入项目目录
cd voice_assistant

# 创建模型目录
mkdir -p models

# Python脚本自动下载Whisper模型
python -c "
import whisper
model = whisper.load_model('base')
print('Whisper base model downloaded successfully')
"
```

### 5. 配置文件设置

#### 环境变量配置
```bash
# 创建.env文件
cat > .env << EOF
# Flask配置
FLASK_ENV=production
FLASK_DEBUG=False
HOST=0.0.0.0
PORT=5000

# Ollama配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2.5:7b
OLLAMA_TIMEOUT=30

# ASR配置
ASR_MODEL=whisper
WHISPER_MODEL_SIZE=base
ASR_LANGUAGE=zh

# TTS配置
TTS_ENGINE=edge-tts
TTS_VOICE=zh-CN-XiaoxiaoNeural

# VAD配置
VAD_ENGINE=webrtcvad
VAD_AGGRESSIVENESS=2
VAD_SILENCE_THRESHOLD=1.5

# 工具配置
TOOLS_ENABLED=True
TOOLS_CONFIDENCE_THRESHOLD=0.8

# 日志配置
LOG_LEVEL=INFO
EOF
```

### 6. 启动应用

#### 开发模式启动
```bash
# 激活虚拟环境
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 启动应用
python app.py
```

#### 生产模式启动
```bash
# 使用gunicorn启动 (推荐)
pip install gunicorn

# 启动命令
gunicorn --worker-class eventlet -w 1 --bind 0.0.0.0:5000 app:app

# 或使用systemd服务 (Linux)
sudo cp docs/voice-assistant.service /etc/systemd/system/
sudo systemctl enable voice-assistant
sudo systemctl start voice-assistant
```

### 7. 防火墙和网络配置

#### 开放端口
```bash
# Ubuntu/Debian
sudo ufw allow 5000
sudo ufw allow 11434

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --permanent --add-port=11434/tcp
sudo firewall-cmd --reload

# Windows
# 在Windows防火墙中添加入站规则，开放5000和11434端口
```

### 8. 访问应用

打开浏览器访问: `http://localhost:5000`

如果部署在服务器上，访问: `http://服务器IP:5000`

## Docker部署 (可选)

### 创建Dockerfile
```dockerfile
FROM python:3.10-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    portaudio19-dev \
    ffmpeg \
    espeak \
    alsa-utils \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["python", "app.py"]
```

### 构建和运行
```bash
# 构建镜像
docker build -t voice-assistant .

# 运行容器
docker run -d \
  --name voice-assistant \
  -p 5000:5000 \
  -v $(pwd)/models:/app/models \
  voice-assistant
```

## 故障排除

### 常见问题

#### 1. 麦克风权限问题
- **症状**: 无法录音，提示权限被拒绝
- **解决**: 在浏览器中允许麦克风权限，检查系统音频设备

#### 2. Whisper模型下载失败
- **症状**: ASR初始化失败
- **解决**: 手动下载模型文件，检查网络连接

#### 3. Ollama连接失败
- **症状**: LLM处理失败
- **解决**: 检查Ollama服务状态，确认端口配置

#### 4. TTS合成失败
- **症状**: 无法播放语音回复
- **解决**: 检查edge-tts网络连接，尝试其他TTS引擎

#### 5. WebSocket连接问题
- **症状**: 前端无法连接后端
- **解决**: 检查防火墙设置，确认端口开放

### 性能优化

#### 1. 模型优化
```bash
# 使用更小的Whisper模型
export WHISPER_MODEL_SIZE=tiny

# 使用量化的Ollama模型
ollama pull qwen2.5:7b-q4_0
```

#### 2. 系统优化
```bash
# 增加系统文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化音频缓冲区
echo "options snd-hda-intel model=generic" >> /etc/modprobe.d/alsa-base.conf
```

### 监控和日志

#### 查看应用日志
```bash
# 实时查看日志
tail -f voice_assistant.log

# 查看错误日志
grep ERROR voice_assistant.log
```

#### 系统监控
```bash
# 查看资源使用
htop
nvidia-smi  # 如果使用GPU

# 查看端口占用
netstat -tlnp | grep :5000
```

## 安全建议

1. **网络安全**
   - 使用HTTPS (配置SSL证书)
   - 限制访问IP范围
   - 设置防火墙规则

2. **应用安全**
   - 定期更新依赖包
   - 使用强密码和密钥
   - 限制文件上传大小

3. **数据安全**
   - 不记录敏感语音数据
   - 定期清理临时文件
   - 加密存储配置信息

## 更新和维护

### 更新应用
```bash
# 备份当前版本
cp -r voice_assistant voice_assistant_backup

# 更新代码
git pull origin main

# 更新依赖
pip install -r requirements.txt --upgrade

# 重启服务
sudo systemctl restart voice-assistant
```

### 定期维护
```bash
# 清理临时文件
find /tmp -name "tmp*" -mtime +7 -delete

# 清理日志文件
logrotate /etc/logrotate.d/voice-assistant

# 更新模型
ollama pull qwen2.5:7b
```

## 联系支持

如果遇到问题，请查看:
- 项目文档: `docs/`
- 故障排除指南: `docs/troubleshooting.md`
- 常见问题: `docs/faq.md`
