"""
语音识别(ASR)模块
集成Whisper模型进行离线中文语音识别
"""
import os
import io
import logging
import tempfile
import asyncio
from typing import Optional, Union, Dict, Any
import threading
import time

try:
    import whisper
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False
    logging.warning("OpenAI Whisper not available")

try:
    import speech_recognition as sr
    SPEECH_RECOGNITION_AVAILABLE = True
except ImportError:
    SPEECH_RECOGNITION_AVAILABLE = False
    logging.warning("SpeechRecognition not available")

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    logging.warning("PyTorch not available")

import numpy as np
import wave
from pathlib import Path

from ..config import Config

logger = logging.getLogger(__name__)

class ASREngine:
    """语音识别引擎基类"""
    
    def __init__(self, config: Config):
        self.config = config
        self.sample_rate = config.ASR_SAMPLE_RATE
        self.language = config.ASR_LANGUAGE
        
    def transcribe(self, audio_data: Union[bytes, str, Path]) -> Dict[str, Any]:
        """
        转录音频
        
        Args:
            audio_data: 音频数据（bytes）或文件路径
            
        Returns:
            Dict: 包含text和confidence的结果
        """
        raise NotImplementedError
    
    def transcribe_stream(self, audio_stream) -> str:
        """
        流式转录音频
        
        Args:
            audio_stream: 音频流
            
        Returns:
            str: 识别结果
        """
        raise NotImplementedError

class WhisperASR(ASREngine):
    """基于OpenAI Whisper的语音识别"""
    
    def __init__(self, config: Config):
        super().__init__(config)
        self.model = None
        self.model_size = config.WHISPER_MODEL_SIZE
        self.model_path = config.WHISPER_MODEL_PATH
        
        if not WHISPER_AVAILABLE:
            raise ImportError("OpenAI Whisper is not available")
        
        self._load_model()
    
    def _load_model(self):
        """加载Whisper模型"""
        try:
            # 检查本地模型文件是否存在
            if self.model_path.exists():
                logger.info(f"Loading Whisper model from {self.model_path}")
                self.model = whisper.load_model(str(self.model_path))
            else:
                logger.info(f"Downloading Whisper {self.model_size} model...")
                self.model = whisper.load_model(self.model_size)
                
                # 保存模型到本地
                if hasattr(self.model, 'save'):
                    self.model_path.parent.mkdir(parents=True, exist_ok=True)
                    # Whisper模型会自动缓存，无需手动保存
                    
            logger.info(f"Whisper model {self.model_size} loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load Whisper model: {e}")
            raise
    
    def transcribe(self, audio_data: Union[bytes, str, Path]) -> Dict[str, Any]:
        """
        使用Whisper转录音频
        
        Args:
            audio_data: 音频数据或文件路径
            
        Returns:
            Dict: 转录结果
        """
        try:
            # 处理不同类型的输入
            if isinstance(audio_data, bytes):
                # 将bytes数据保存为临时文件
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                    self._save_audio_bytes(audio_data, temp_file.name)
                    audio_path = temp_file.name
            else:
                audio_path = str(audio_data)
            
            # 使用Whisper进行转录
            result = self.model.transcribe(
                audio_path,
                language=self.language if self.language != 'auto' else None,
                task='transcribe',
                fp16=TORCH_AVAILABLE and torch.cuda.is_available(),
                verbose=False
            )
            
            # 清理临时文件
            if isinstance(audio_data, bytes):
                try:
                    os.unlink(audio_path)
                except:
                    pass
            
            return {
                'text': result['text'].strip(),
                'confidence': self._calculate_confidence(result),
                'language': result.get('language', self.language),
                'segments': result.get('segments', [])
            }
            
        except Exception as e:
            logger.error(f"Whisper transcription error: {e}")
            return {
                'text': '',
                'confidence': 0.0,
                'language': self.language,
                'segments': []
            }
    
    def _calculate_confidence(self, result: Dict) -> float:
        """
        计算转录置信度
        
        Args:
            result: Whisper结果
            
        Returns:
            float: 置信度 (0-1)
        """
        try:
            segments = result.get('segments', [])
            if not segments:
                return 0.5
            
            # 计算所有段的平均置信度
            total_confidence = 0.0
            total_duration = 0.0
            
            for segment in segments:
                duration = segment.get('end', 0) - segment.get('start', 0)
                confidence = segment.get('avg_logprob', -1.0)
                
                # 将log概率转换为置信度
                confidence = max(0.0, min(1.0, (confidence + 1.0) / 2.0))
                
                total_confidence += confidence * duration
                total_duration += duration
            
            if total_duration > 0:
                return total_confidence / total_duration
            else:
                return 0.5
                
        except Exception as e:
            logger.error(f"Confidence calculation error: {e}")
            return 0.5
    
    def _save_audio_bytes(self, audio_data: bytes, filename: str):
        """
        将音频bytes保存为WAV文件
        
        Args:
            audio_data: 音频数据
            filename: 输出文件名
        """
        try:
            # 假设音频数据是16-bit PCM格式
            with wave.open(filename, 'wb') as wav_file:
                wav_file.setnchannels(1)  # 单声道
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(self.sample_rate)
                wav_file.writeframes(audio_data)
        except Exception as e:
            logger.error(f"Failed to save audio bytes: {e}")
            raise

class SpeechRecognitionASR(ASREngine):
    """基于SpeechRecognition库的语音识别"""
    
    def __init__(self, config: Config):
        super().__init__(config)
        
        if not SPEECH_RECOGNITION_AVAILABLE:
            raise ImportError("SpeechRecognition is not available")
        
        self.recognizer = sr.Recognizer()
        self.recognizer.energy_threshold = 300
        self.recognizer.dynamic_energy_threshold = True
        self.recognizer.pause_threshold = 0.8
        self.recognizer.phrase_threshold = 0.3
        
    def transcribe(self, audio_data: Union[bytes, str, Path]) -> Dict[str, Any]:
        """
        使用SpeechRecognition转录音频
        
        Args:
            audio_data: 音频数据或文件路径
            
        Returns:
            Dict: 转录结果
        """
        try:
            # 处理不同类型的输入
            if isinstance(audio_data, bytes):
                # 将bytes转换为AudioData
                audio_io = io.BytesIO(audio_data)
                with sr.AudioFile(audio_io) as source:
                    audio = self.recognizer.record(source)
            else:
                # 从文件读取
                with sr.AudioFile(str(audio_data)) as source:
                    audio = self.recognizer.record(source)
            
            # 尝试多种识别引擎
            text = ""
            confidence = 0.0
            
            # 优先使用Google识别（需要网络）
            try:
                text = self.recognizer.recognize_google(audio, language=self.language)
                confidence = 0.8  # Google识别通常较准确
            except sr.RequestError:
                # 网络问题，尝试离线识别
                try:
                    text = self.recognizer.recognize_sphinx(audio, language=self.language)
                    confidence = 0.6  # Sphinx准确度较低
                except:
                    text = ""
                    confidence = 0.0
            except sr.UnknownValueError:
                text = ""
                confidence = 0.0
            
            return {
                'text': text.strip(),
                'confidence': confidence,
                'language': self.language,
                'segments': []
            }
            
        except Exception as e:
            logger.error(f"SpeechRecognition transcription error: {e}")
            return {
                'text': '',
                'confidence': 0.0,
                'language': self.language,
                'segments': []
            }

class ASRManager:
    """ASR管理器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.engine = None
        self._initialize_engine()
        
        # 实时转录相关
        self.is_streaming = False
        self.stream_thread = None
        self.partial_results = []
        
    def _initialize_engine(self):
        """初始化ASR引擎"""
        engine_type = self.config.ASR_MODEL.lower()
        
        try:
            if engine_type == 'whisper' and WHISPER_AVAILABLE:
                self.engine = WhisperASR(self.config)
                logger.info("Whisper ASR engine initialized")
            elif engine_type == 'speech_recognition' and SPEECH_RECOGNITION_AVAILABLE:
                self.engine = SpeechRecognitionASR(self.config)
                logger.info("SpeechRecognition ASR engine initialized")
            else:
                # 回退到可用的引擎
                if WHISPER_AVAILABLE:
                    self.engine = WhisperASR(self.config)
                    logger.info("Fallback to Whisper ASR engine")
                elif SPEECH_RECOGNITION_AVAILABLE:
                    self.engine = SpeechRecognitionASR(self.config)
                    logger.info("Fallback to SpeechRecognition ASR engine")
                else:
                    raise RuntimeError("No ASR engine available")
                    
        except Exception as e:
            logger.error(f"Failed to initialize ASR engine: {e}")
            raise
    
    def transcribe(self, audio_data: Union[bytes, str, Path]) -> Dict[str, Any]:
        """
        转录音频
        
        Args:
            audio_data: 音频数据或文件路径
            
        Returns:
            Dict: 转录结果
        """
        if self.engine is None:
            return {
                'text': '',
                'confidence': 0.0,
                'language': self.config.ASR_LANGUAGE,
                'segments': []
            }
        
        start_time = time.time()
        result = self.engine.transcribe(audio_data)
        end_time = time.time()
        
        result['processing_time'] = end_time - start_time
        logger.info(f"ASR transcription completed in {result['processing_time']:.2f}s: {result['text']}")
        
        return result
    
    def start_streaming(self, callback=None):
        """开始流式识别"""
        self.is_streaming = True
        self.partial_results.clear()
        
        if callback:
            self.stream_callback = callback
        
        logger.info("ASR streaming started")
    
    def stop_streaming(self):
        """停止流式识别"""
        self.is_streaming = False
        
        if self.stream_thread and self.stream_thread.is_alive():
            self.stream_thread.join(timeout=1.0)
        
        logger.info("ASR streaming stopped")
    
    def process_stream_chunk(self, audio_chunk: bytes) -> Optional[str]:
        """
        处理流式音频块
        
        Args:
            audio_chunk: 音频数据块
            
        Returns:
            Optional[str]: 部分识别结果
        """
        if not self.is_streaming:
            return None
        
        # 简单实现：累积音频块并定期识别
        self.partial_results.append(audio_chunk)
        
        # 每收集一定量的数据进行一次识别
        if len(self.partial_results) >= 10:  # 可调整
            combined_audio = b''.join(self.partial_results)
            result = self.transcribe(combined_audio)
            
            # 清空部分结果
            self.partial_results.clear()
            
            return result.get('text', '')
        
        return None
    
    def get_supported_languages(self) -> list:
        """获取支持的语言列表"""
        if isinstance(self.engine, WhisperASR):
            return list(whisper.tokenizer.LANGUAGES.keys())
        else:
            return ['zh', 'en', 'ja', 'ko']  # 常见语言
    
    def set_language(self, language: str):
        """设置识别语言"""
        self.config.ASR_LANGUAGE = language
        self.engine.language = language
        logger.info(f"ASR language set to: {language}")

def create_asr_manager(config: Config) -> ASRManager:
    """
    创建ASR管理器
    
    Args:
        config: 配置对象
        
    Returns:
        ASRManager: ASR管理器实例
    """
    return ASRManager(config)
