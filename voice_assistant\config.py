"""
语音助手系统配置文件
包含所有模型路径、API端点、语音参数等配置项
"""
import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent
MODELS_DIR = PROJECT_ROOT / "models"
STATIC_DIR = PROJECT_ROOT / "static"
AUDIO_DIR = STATIC_DIR / "audio"

# 确保目录存在
MODELS_DIR.mkdir(exist_ok=True)
AUDIO_DIR.mkdir(exist_ok=True)

class Config:
    """主配置类"""
    
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'voice-assistant-secret-key-2024'
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    HOST = os.environ.get('HOST', '127.0.0.1')
    PORT = int(os.environ.get('PORT', 5000))
    
    # WebSocket配置
    SOCKETIO_ASYNC_MODE = 'threading'
    SOCKETIO_CORS_ALLOWED_ORIGINS = "*"
    
    # Ollama配置
    OLLAMA_BASE_URL = os.environ.get('OLLAMA_BASE_URL', 'http://localhost:11434')
    OLLAMA_MODEL = os.environ.get('OLLAMA_MODEL', 'qwen2.5:7b')
    OLLAMA_TIMEOUT = int(os.environ.get('OLLAMA_TIMEOUT', 30))
    OLLAMA_TEMPERATURE = float(os.environ.get('OLLAMA_TEMPERATURE', 0.7))
    OLLAMA_MAX_TOKENS = int(os.environ.get('OLLAMA_MAX_TOKENS', 2048))
    
    # 语音识别(ASR)配置
    ASR_MODEL = os.environ.get('ASR_MODEL', 'whisper')  # whisper, speech_recognition
    WHISPER_MODEL_SIZE = os.environ.get('WHISPER_MODEL_SIZE', 'base')  # tiny, base, small, medium, large
    WHISPER_MODEL_PATH = MODELS_DIR / f"whisper-{WHISPER_MODEL_SIZE}.pt"
    ASR_LANGUAGE = os.environ.get('ASR_LANGUAGE', 'zh')  # 中文
    ASR_SAMPLE_RATE = int(os.environ.get('ASR_SAMPLE_RATE', 16000))
    ASR_CHUNK_SIZE = int(os.environ.get('ASR_CHUNK_SIZE', 1024))
    
    # 语音合成(TTS)配置
    TTS_ENGINE = os.environ.get('TTS_ENGINE', 'edge-tts')  # edge-tts, pyttsx3, gtts
    TTS_VOICE = os.environ.get('TTS_VOICE', 'zh-CN-XiaoxiaoNeural')  # Edge-TTS中文女声
    TTS_RATE = os.environ.get('TTS_RATE', '+0%')  # 语速
    TTS_VOLUME = os.environ.get('TTS_VOLUME', '+0%')  # 音量
    TTS_PITCH = os.environ.get('TTS_PITCH', '+0Hz')  # 音调
    TTS_OUTPUT_FORMAT = os.environ.get('TTS_OUTPUT_FORMAT', 'audio-24khz-48kbitrate-mono-mp3')
    
    # 语音活动检测(VAD)配置
    VAD_ENGINE = os.environ.get('VAD_ENGINE', 'webrtcvad')  # webrtcvad, silero
    VAD_AGGRESSIVENESS = int(os.environ.get('VAD_AGGRESSIVENESS', 2))  # 0-3, 越高越敏感
    VAD_FRAME_DURATION = int(os.environ.get('VAD_FRAME_DURATION', 30))  # ms: 10, 20, 30
    VAD_SILENCE_THRESHOLD = float(os.environ.get('VAD_SILENCE_THRESHOLD', 1.5))  # 静音检测阈值(秒)
    VAD_MAX_RECORDING_TIME = int(os.environ.get('VAD_MAX_RECORDING_TIME', 30))  # 最大录音时长(秒)
    
    # 音频处理配置
    AUDIO_SAMPLE_RATE = int(os.environ.get('AUDIO_SAMPLE_RATE', 16000))
    AUDIO_CHANNELS = int(os.environ.get('AUDIO_CHANNELS', 1))  # 单声道
    AUDIO_CHUNK_SIZE = int(os.environ.get('AUDIO_CHUNK_SIZE', 1024))
    AUDIO_FORMAT = os.environ.get('AUDIO_FORMAT', 'wav')
    
    # 对话上下文配置
    CONTEXT_MAX_TURNS = int(os.environ.get('CONTEXT_MAX_TURNS', 10))  # 最大对话轮数
    CONTEXT_MAX_TOKENS = int(os.environ.get('CONTEXT_MAX_TOKENS', 4000))  # 最大上下文token数
    
    # 工具调用配置
    TOOLS_ENABLED = os.environ.get('TOOLS_ENABLED', 'True').lower() == 'true'
    TOOLS_CONFIDENCE_THRESHOLD = float(os.environ.get('TOOLS_CONFIDENCE_THRESHOLD', 0.8))
    
    # 性能配置
    MAX_CONCURRENT_REQUESTS = int(os.environ.get('MAX_CONCURRENT_REQUESTS', 5))
    REQUEST_TIMEOUT = int(os.environ.get('REQUEST_TIMEOUT', 30))
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = PROJECT_ROOT / 'logs' / 'voice_assistant.log'
    
    # 错误处理配置
    MAX_RETRIES = int(os.environ.get('MAX_RETRIES', 3))
    RETRY_DELAY = float(os.environ.get('RETRY_DELAY', 1.0))

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

# 配置映射
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config(config_name=None):
    """获取配置对象"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    return config_map.get(config_name, DevelopmentConfig)

# 模型下载URLs
MODEL_URLS = {
    'whisper-tiny': 'https://openaipublic.azureedge.net/main/whisper/models/65147644a518d12f04e32d6f3b26facc3f8dd46e/tiny.pt',
    'whisper-base': 'https://openaipublic.azureedge.net/main/whisper/models/ed3a0b6b1c0edf879ad9b11b1af5a0e6ab5db9205f891f668f8b0e6c6326e34e/base.pt',
    'whisper-small': 'https://openaipublic.azureedge.net/main/whisper/models/9ecf779972d90ba49c06d968637d720dd632c55bbf19d441fb42bf17a411e794/small.pt',
    'whisper-medium': 'https://openaipublic.azureedge.net/main/whisper/models/345ae4da62f9b3d59415adc60127b97c714f32e89e936602e85993674d08dcb1/medium.pt',
    'whisper-large': 'https://openaipublic.azureedge.net/main/whisper/models/e4b87e7e0bf463eb8e6956e646f1e277e901512310def2c24bf0e11bd3c28e9a/large-v3.pt'
}

# 支持的语言列表
SUPPORTED_LANGUAGES = {
    'zh': '中文',
    'en': 'English',
    'ja': '日本語',
    'ko': '한국어',
    'es': 'Español',
    'fr': 'Français',
    'de': 'Deutsch',
    'ru': 'Русский'
}

# TTS语音列表
TTS_VOICES = {
    'zh-CN-XiaoxiaoNeural': '中文女声(晓晓)',
    'zh-CN-YunxiNeural': '中文男声(云希)',
    'zh-CN-YunyangNeural': '中文男声(云扬)',
    'zh-CN-XiaoyiNeural': '中文女声(晓伊)',
    'zh-CN-YunjianNeural': '中文男声(云健)',
    'zh-CN-XiaochenNeural': '中文女声(晓辰)',
    'zh-CN-XiaohanNeural': '中文女声(晓涵)',
    'zh-CN-XiaomengNeural': '中文女声(晓梦)',
    'zh-CN-XiaomoNeural': '中文女声(晓墨)',
    'zh-CN-XiaoqiuNeural': '中文女声(晓秋)',
    'zh-CN-XiaoruiNeural': '中文女声(晓睿)',
    'zh-CN-XiaoshuangNeural': '中文女声(晓双)',
    'zh-CN-XiaoxuanNeural': '中文女声(晓萱)',
    'zh-CN-XiaoyanNeural': '中文女声(晓颜)',
    'zh-CN-XiaoyouNeural': '中文女声(晓悠)',
    'zh-CN-XiaozhenNeural': '中文女声(晓甄)',
    'zh-CN-YunfengNeural': '中文男声(云枫)',
    'zh-CN-YunhaoNeural': '中文男声(云皓)',
    'zh-CN-YunxiaNeural': '中文男声(云夏)',
    'zh-CN-YunyeNeural': '中文男声(云野)',
    'zh-CN-YunzeNeural': '中文男声(云泽)'
}

# 预置工具配置
PRESET_TOOLS = {
    'identity_card': {
        'name': '办理身份证',
        'keywords': ['身份证', '办证', '证件', '户籍'],
        'description': '身份证办理流程指导',
        'enabled': True
    },
    'weather': {
        'name': '查询天气',
        'keywords': ['天气', '气温', '下雨', '晴天', '阴天'],
        'description': '本地天气信息查询',
        'enabled': True
    },
    'reminder': {
        'name': '设置提醒',
        'keywords': ['提醒', '闹钟', '定时', '记住'],
        'description': '创建语音提醒功能',
        'enabled': True
    },
    'music': {
        'name': '播放音乐',
        'keywords': ['音乐', '歌曲', '播放', '暂停', '下一首'],
        'description': '音乐播放控制',
        'enabled': True
    }
}
