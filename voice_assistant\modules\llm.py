"""
大语言模型接口模块
集成Ollama API并处理流式响应和内容过滤
"""
import json
import logging
import re
import time
import asyncio
from typing import Dict, List, Optional, Generator, Any, Callable
import threading
import queue

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    logging.error("requests library not available")

try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False
    logging.warning("aiohttp not available, async features disabled")

from ..config import Config

logger = logging.getLogger(__name__)

class ConversationContext:
    """对话上下文管理"""
    
    def __init__(self, max_turns: int = 10, max_tokens: int = 4000):
        self.max_turns = max_turns
        self.max_tokens = max_tokens
        self.messages = []
        self.total_tokens = 0
    
    def add_message(self, role: str, content: str, tokens: int = None):
        """添加消息到上下文"""
        if tokens is None:
            tokens = len(content) // 4  # 粗略估算token数
        
        message = {
            'role': role,
            'content': content,
            'timestamp': time.time(),
            'tokens': tokens
        }
        
        self.messages.append(message)
        self.total_tokens += tokens
        
        # 清理旧消息
        self._cleanup_old_messages()
    
    def _cleanup_old_messages(self):
        """清理旧消息以保持上下文大小"""
        # 保留系统消息
        system_messages = [msg for msg in self.messages if msg['role'] == 'system']
        other_messages = [msg for msg in self.messages if msg['role'] != 'system']
        
        # 按时间排序，保留最新的消息
        other_messages.sort(key=lambda x: x['timestamp'])
        
        # 清理超出限制的消息
        while (len(other_messages) > self.max_turns * 2 or 
               self.total_tokens > self.max_tokens) and other_messages:
            removed = other_messages.pop(0)
            self.total_tokens -= removed['tokens']
        
        self.messages = system_messages + other_messages
    
    def get_messages_for_api(self) -> List[Dict[str, str]]:
        """获取用于API调用的消息格式"""
        return [{'role': msg['role'], 'content': msg['content']} 
                for msg in self.messages]
    
    def clear(self):
        """清空上下文"""
        self.messages.clear()
        self.total_tokens = 0

class ContentFilter:
    """内容过滤器"""
    
    def __init__(self):
        # 思考标签模式
        self.thinking_patterns = [
            re.compile(r'<think>.*?</think>', re.DOTALL | re.IGNORECASE),
            re.compile(r'<thinking>.*?</thinking>', re.DOTALL | re.IGNORECASE),
            re.compile(r'<thought>.*?</thought>', re.DOTALL | re.IGNORECASE),
            re.compile(r'<analysis>.*?</analysis>', re.DOTALL | re.IGNORECASE),
            re.compile(r'<reasoning>.*?</reasoning>', re.DOTALL | re.IGNORECASE),
        ]
        
        # 其他需要过滤的模式
        self.filter_patterns = [
            re.compile(r'<\|.*?\|>', re.DOTALL),  # 特殊标记
            re.compile(r'\[INST\].*?\[/INST\]', re.DOTALL),  # 指令标记
            re.compile(r'<s>|</s>', re.IGNORECASE),  # 序列标记
        ]
    
    def filter_content(self, text: str) -> str:
        """
        过滤文本内容
        
        Args:
            text: 原始文本
            
        Returns:
            str: 过滤后的文本
        """
        if not text:
            return text
        
        filtered_text = text
        
        # 移除思考标签
        for pattern in self.thinking_patterns:
            filtered_text = pattern.sub('', filtered_text)
        
        # 移除其他标记
        for pattern in self.filter_patterns:
            filtered_text = pattern.sub('', filtered_text)
        
        # 清理多余的空白字符
        filtered_text = re.sub(r'\n\s*\n', '\n', filtered_text)
        filtered_text = re.sub(r'[ \t]+', ' ', filtered_text)
        filtered_text = filtered_text.strip()
        
        return filtered_text
    
    def extract_thinking(self, text: str) -> str:
        """
        提取思考内容（用于调试）
        
        Args:
            text: 原始文本
            
        Returns:
            str: 思考内容
        """
        thinking_content = []
        
        for pattern in self.thinking_patterns:
            matches = pattern.findall(text)
            thinking_content.extend(matches)
        
        return '\n'.join(thinking_content)

class OllamaClient:
    """Ollama API客户端"""
    
    def __init__(self, config: Config):
        self.config = config
        self.base_url = config.OLLAMA_BASE_URL
        self.model = config.OLLAMA_MODEL
        self.timeout = config.OLLAMA_TIMEOUT
        self.temperature = config.OLLAMA_TEMPERATURE
        self.max_tokens = config.OLLAMA_MAX_TOKENS
        
        self.session = None
        if REQUESTS_AVAILABLE:
            self.session = requests.Session()
            self.session.timeout = self.timeout
        
        # 内容过滤器
        self.content_filter = ContentFilter()
        
        # 测试连接
        self._test_connection()
    
    def _test_connection(self):
        """测试Ollama连接"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                logger.info("Ollama connection successful")
                models = response.json().get('models', [])
                model_names = [model['name'] for model in models]
                logger.info(f"Available models: {model_names}")
                
                if self.model not in model_names:
                    logger.warning(f"Model {self.model} not found in available models")
            else:
                logger.warning(f"Ollama connection test failed: {response.status_code}")
        except Exception as e:
            logger.error(f"Failed to connect to Ollama: {e}")
    
    def generate(self, messages: List[Dict[str, str]], 
                 stream: bool = False) -> Dict[str, Any]:
        """
        生成回复
        
        Args:
            messages: 对话消息列表
            stream: 是否使用流式响应
            
        Returns:
            Dict: 生成结果
        """
        try:
            # 构建请求数据
            data = {
                'model': self.model,
                'messages': messages,
                'stream': stream,
                'options': {
                    'temperature': self.temperature,
                    'num_predict': self.max_tokens,
                }
            }
            
            if stream:
                return self._generate_stream(data)
            else:
                return self._generate_single(data)
                
        except Exception as e:
            logger.error(f"Ollama generation error: {e}")
            return {
                'success': False,
                'error': str(e),
                'content': '抱歉，我现在无法回答您的问题。'
            }
    
    def _generate_single(self, data: Dict) -> Dict[str, Any]:
        """单次生成"""
        try:
            response = self.session.post(
                f"{self.base_url}/api/chat",
                json=data,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('message', {}).get('content', '')
                
                # 过滤内容
                filtered_content = self.content_filter.filter_content(content)
                
                return {
                    'success': True,
                    'content': filtered_content,
                    'raw_content': content,
                    'model': result.get('model', self.model),
                    'done': result.get('done', True)
                }
            else:
                logger.error(f"Ollama API error: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f"API error: {response.status_code}",
                    'content': '服务暂时不可用，请稍后再试。'
                }
                
        except Exception as e:
            logger.error(f"Single generation error: {e}")
            return {
                'success': False,
                'error': str(e),
                'content': '生成回复时出现错误。'
            }
    
    def _generate_stream(self, data: Dict) -> Dict[str, Any]:
        """流式生成"""
        try:
            response = self.session.post(
                f"{self.base_url}/api/chat",
                json=data,
                stream=True,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'stream': self._parse_stream_response(response),
                    'content': ''  # 流式响应中内容通过stream获取
                }
            else:
                logger.error(f"Ollama stream API error: {response.status_code}")
                return {
                    'success': False,
                    'error': f"Stream API error: {response.status_code}",
                    'content': '流式响应失败。'
                }
                
        except Exception as e:
            logger.error(f"Stream generation error: {e}")
            return {
                'success': False,
                'error': str(e),
                'content': '流式生成时出现错误。'
            }
    
    def _parse_stream_response(self, response) -> Generator[str, None, None]:
        """解析流式响应"""
        try:
            full_content = ""
            
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    try:
                        data = json.loads(line)
                        if 'message' in data and 'content' in data['message']:
                            chunk = data['message']['content']
                            full_content += chunk
                            
                            # 实时过滤并返回增量内容
                            filtered_full = self.content_filter.filter_content(full_content)
                            yield chunk
                            
                        if data.get('done', False):
                            break
                            
                    except json.JSONDecodeError:
                        continue
                        
        except Exception as e:
            logger.error(f"Stream parsing error: {e}")
            yield "流式解析出现错误。"

class LLMManager:
    """LLM管理器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.client = OllamaClient(config)
        self.context = ConversationContext(
            max_turns=config.CONTEXT_MAX_TURNS,
            max_tokens=config.CONTEXT_MAX_TOKENS
        )
        
        # 系统提示词
        self.system_prompt = self._get_system_prompt()
        self.context.add_message('system', self.system_prompt)
        
        # 回调函数
        self.response_callback = None
        self.stream_callback = None
    
    def _get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """你是一个智能语音助手，专门通过语音与用户进行自然对话。

请遵循以下原则：
1. 回复要简洁明了，适合语音播放
2. 使用自然、友好的语调
3. 避免过长的回复，控制在50字以内
4. 如果需要详细解释，可以询问用户是否需要更多信息
5. 对于不确定的信息，要诚实说明
6. 支持中文对话，回复要符合中文表达习惯

你可以帮助用户：
- 回答各种问题
- 进行日常对话
- 提供建议和帮助
- 调用相关工具（如查天气、设提醒等）

请始终保持礼貌和耐心。"""
    
    def chat(self, user_input: str, stream: bool = False) -> Dict[str, Any]:
        """
        与LLM对话
        
        Args:
            user_input: 用户输入
            stream: 是否使用流式响应
            
        Returns:
            Dict: 对话结果
        """
        try:
            # 添加用户消息到上下文
            self.context.add_message('user', user_input)
            
            # 获取对话消息
            messages = self.context.get_messages_for_api()
            
            # 生成回复
            result = self.client.generate(messages, stream=stream)
            
            if result.get('success', False):
                content = result.get('content', '')
                if content:
                    # 添加助手回复到上下文
                    self.context.add_message('assistant', content)
                
                # 调用回调函数
                if self.response_callback:
                    self.response_callback(content)
            
            return result
            
        except Exception as e:
            logger.error(f"Chat error: {e}")
            return {
                'success': False,
                'error': str(e),
                'content': '对话处理出现错误。'
            }
    
    def chat_stream(self, user_input: str) -> Generator[str, None, None]:
        """
        流式对话
        
        Args:
            user_input: 用户输入
            
        Yields:
            str: 流式回复内容
        """
        try:
            # 添加用户消息到上下文
            self.context.add_message('user', user_input)
            
            # 获取对话消息
            messages = self.context.get_messages_for_api()
            
            # 生成流式回复
            result = self.client.generate(messages, stream=True)
            
            if result.get('success', False) and 'stream' in result:
                full_content = ""
                
                for chunk in result['stream']:
                    full_content += chunk
                    yield chunk
                    
                    # 调用流式回调
                    if self.stream_callback:
                        self.stream_callback(chunk)
                
                # 添加完整回复到上下文
                if full_content:
                    filtered_content = self.client.content_filter.filter_content(full_content)
                    self.context.add_message('assistant', filtered_content)
            else:
                yield result.get('content', '流式对话失败。')
                
        except Exception as e:
            logger.error(f"Stream chat error: {e}")
            yield '流式对话出现错误。'
    
    def set_response_callback(self, callback: Callable[[str], None]):
        """设置响应回调函数"""
        self.response_callback = callback
    
    def set_stream_callback(self, callback: Callable[[str], None]):
        """设置流式回调函数"""
        self.stream_callback = callback
    
    def clear_context(self):
        """清空对话上下文"""
        self.context.clear()
        self.context.add_message('system', self.system_prompt)
        logger.info("Conversation context cleared")
    
    def get_context_info(self) -> Dict[str, Any]:
        """获取上下文信息"""
        return {
            'message_count': len(self.context.messages),
            'total_tokens': self.context.total_tokens,
            'max_turns': self.context.max_turns,
            'max_tokens': self.context.max_tokens
        }

def create_llm_manager(config: Config) -> LLMManager:
    """
    创建LLM管理器
    
    Args:
        config: 配置对象
        
    Returns:
        LLMManager: LLM管理器实例
    """
    return LLMManager(config)
