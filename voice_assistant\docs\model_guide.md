# 语音助手模型选择指南

## 概述

本指南详细介绍了语音助手系统中各个模块的模型选择建议，包括语音识别(ASR)、语音合成(TTS)、大语言模型(LLM)和语音活动检测(VAD)。

## 语音识别(ASR)模型

### OpenAI Whisper (推荐)

#### 模型大小对比
| 模型 | 参数量 | 内存占用 | 速度 | 准确率 | 适用场景 |
|------|--------|----------|------|--------|----------|
| tiny | 39M | ~1GB | 很快 | 一般 | 实时应用，资源受限 |
| base | 74M | ~1GB | 快 | 良好 | **推荐默认选择** |
| small | 244M | ~2GB | 中等 | 很好 | 平衡性能和准确率 |
| medium | 769M | ~5GB | 慢 | 优秀 | 高准确率要求 |
| large | 1550M | ~10GB | 很慢 | 最佳 | 离线批处理 |

#### 配置建议
```python
# 快速响应 (< 1秒)
WHISPER_MODEL_SIZE = "tiny"
ASR_LANGUAGE = "zh"

# 平衡模式 (推荐)
WHISPER_MODEL_SIZE = "base"
ASR_LANGUAGE = "zh"

# 高精度模式
WHISPER_MODEL_SIZE = "small"
ASR_LANGUAGE = "zh"
```

#### 语言支持
- **中文**: 支持普通话，准确率85-95%
- **英文**: 支持美式/英式英语，准确率90-98%
- **多语言**: 支持99种语言自动检测

### SpeechRecognition (备选)

#### 引擎对比
| 引擎 | 网络要求 | 准确率 | 延迟 | 成本 |
|------|----------|--------|------|------|
| Google | 需要 | 很高 | 低 | 免费额度 |
| Sphinx | 离线 | 中等 | 中等 | 免费 |
| Wit.ai | 需要 | 高 | 低 | 免费 |

## 语音合成(TTS)模型

### Edge-TTS (推荐)

#### 中文语音选择
| 语音ID | 性别 | 特点 | 适用场景 |
|--------|------|------|----------|
| zh-CN-XiaoxiaoNeural | 女 | 温和自然 | **通用推荐** |
| zh-CN-YunxiNeural | 男 | 稳重专业 | 商务场景 |
| zh-CN-XiaohanNeural | 女 | 活泼年轻 | 娱乐应用 |
| zh-CN-YunyangNeural | 男 | 磁性成熟 | 新闻播报 |

#### 配置示例
```python
# 默认配置
TTS_ENGINE = "edge-tts"
TTS_VOICE = "zh-CN-XiaoxiaoNeural"
TTS_RATE = "+0%"
TTS_VOLUME = "+0%"
TTS_PITCH = "+0Hz"

# 快速语音
TTS_RATE = "+20%"
TTS_PITCH = "+50Hz"

# 慢速清晰
TTS_RATE = "-20%"
TTS_VOLUME = "+10%"
```

### pyttsx3 (离线备选)

#### 优缺点
- **优点**: 完全离线，无网络依赖
- **缺点**: 语音质量一般，中文支持有限
- **适用**: 网络受限环境

### gTTS (在线备选)

#### 特点
- **优点**: Google质量，多语言支持
- **缺点**: 需要网络，有使用限制
- **适用**: 对语音质量要求高的场景

## 大语言模型(LLM)

### Ollama支持的模型

#### 中文优化模型 (推荐)
| 模型 | 大小 | 内存需求 | 中文能力 | 推理速度 | 适用场景 |
|------|------|----------|----------|----------|----------|
| qwen2.5:7b | 4.4GB | 8GB | 优秀 | 快 | **推荐默认** |
| qwen2.5:14b | 8.2GB | 16GB | 很好 | 中等 | 高质量对话 |
| qwen2.5:32b | 18GB | 32GB | 最佳 | 慢 | 专业应用 |
| chatglm3:6b | 3.7GB | 6GB | 很好 | 快 | 资源受限 |

#### 通用模型
| 模型 | 大小 | 内存需求 | 能力 | 中文支持 |
|------|------|----------|------|----------|
| llama3.1:8b | 4.7GB | 8GB | 很好 | 一般 |
| gemma2:9b | 5.4GB | 10GB | 优秀 | 一般 |
| mistral:7b | 4.1GB | 8GB | 良好 | 较差 |

#### 模型选择建议
```bash
# 资源受限 (4-8GB内存)
ollama pull qwen2.5:7b-q4_0  # 量化版本

# 标准配置 (8-16GB内存)
ollama pull qwen2.5:7b

# 高性能 (16GB+内存)
ollama pull qwen2.5:14b

# 专业应用 (32GB+内存)
ollama pull qwen2.5:32b
```

### 模型参数调优

#### 温度设置
```python
# 创意对话
OLLAMA_TEMPERATURE = 0.8

# 平衡模式 (推荐)
OLLAMA_TEMPERATURE = 0.7

# 准确回答
OLLAMA_TEMPERATURE = 0.3
```

#### 其他参数
```python
# 最大输出长度
OLLAMA_MAX_TOKENS = 2048  # 推荐值

# 超时设置
OLLAMA_TIMEOUT = 30  # 秒

# 上下文长度
CONTEXT_MAX_TOKENS = 4000
```

## 语音活动检测(VAD)

### WebRTC VAD (推荐)

#### 敏感度设置
| 级别 | 值 | 特点 | 适用场景 |
|------|----|----- |----------|
| 最低 | 0 | 不敏感，漏检多 | 嘈杂环境 |
| 低 | 1 | 较不敏感 | 一般环境 |
| 中 | 2 | 平衡 | **推荐默认** |
| 高 | 3 | 敏感，误检多 | 安静环境 |

#### 配置示例
```python
# 安静环境
VAD_AGGRESSIVENESS = 3
VAD_SILENCE_THRESHOLD = 1.0

# 一般环境 (推荐)
VAD_AGGRESSIVENESS = 2
VAD_SILENCE_THRESHOLD = 1.5

# 嘈杂环境
VAD_AGGRESSIVENESS = 1
VAD_SILENCE_THRESHOLD = 2.0
```

### Silero VAD (高级)

#### 特点
- **优点**: 基于深度学习，准确率高
- **缺点**: 需要PyTorch，资源占用大
- **适用**: 对检测精度要求高的场景

## 硬件配置建议

### 最低配置
- **CPU**: 4核心 2.0GHz
- **内存**: 8GB
- **存储**: 10GB
- **模型组合**: Whisper tiny + qwen2.5:7b-q4_0 + Edge-TTS

### 推荐配置
- **CPU**: 8核心 3.0GHz
- **内存**: 16GB
- **存储**: 20GB
- **模型组合**: Whisper base + qwen2.5:7b + Edge-TTS

### 高性能配置
- **CPU**: 16核心 3.5GHz
- **内存**: 32GB
- **存储**: 50GB
- **GPU**: RTX 3060或以上
- **模型组合**: Whisper small + qwen2.5:14b + Edge-TTS

## 性能优化建议

### 1. 模型量化
```bash
# 下载量化模型减少内存占用
ollama pull qwen2.5:7b-q4_0  # 4-bit量化
ollama pull qwen2.5:7b-q8_0  # 8-bit量化
```

### 2. GPU加速
```python
# 启用GPU加速 (需要CUDA)
import torch
if torch.cuda.is_available():
    device = "cuda"
else:
    device = "cpu"
```

### 3. 并发优化
```python
# 限制并发请求数
MAX_CONCURRENT_REQUESTS = 3

# 设置超时时间
REQUEST_TIMEOUT = 30
```

### 4. 缓存策略
```python
# 启用模型缓存
WHISPER_CACHE_DIR = "./models/whisper_cache"
OLLAMA_CACHE_SIZE = "4GB"
```

## 模型更新和维护

### 定期更新
```bash
# 更新Ollama模型
ollama pull qwen2.5:7b

# 更新Whisper模型
pip install --upgrade openai-whisper

# 检查模型版本
ollama list
```

### 模型备份
```bash
# 备份Ollama模型
cp -r ~/.ollama/models ./backup/ollama_models

# 备份Whisper模型
cp -r ~/.cache/whisper ./backup/whisper_models
```

### 监控模型性能
```python
# 记录响应时间
import time

start_time = time.time()
result = model.process(input_data)
processing_time = time.time() - start_time

# 记录准确率
accuracy = calculate_accuracy(result, ground_truth)
```

## 故障排除

### 常见问题

#### 1. 模型下载失败
```bash
# 手动下载
wget https://huggingface.co/openai/whisper-base/resolve/main/pytorch_model.bin

# 设置代理
export https_proxy=http://proxy:port
ollama pull qwen2.5:7b
```

#### 2. 内存不足
```bash
# 使用更小的模型
ollama pull qwen2.5:7b-q4_0

# 限制并发
MAX_CONCURRENT_REQUESTS = 1
```

#### 3. 推理速度慢
```bash
# 启用GPU
export CUDA_VISIBLE_DEVICES=0

# 使用量化模型
ollama pull qwen2.5:7b-q8_0
```

## 总结

选择合适的模型组合对系统性能至关重要：

1. **入门配置**: Whisper tiny + qwen2.5:7b-q4_0 + Edge-TTS
2. **推荐配置**: Whisper base + qwen2.5:7b + Edge-TTS  
3. **高端配置**: Whisper small + qwen2.5:14b + Edge-TTS

根据实际硬件资源和性能需求选择合适的模型，并定期监控和优化系统性能。
