/**
 * 语音助手主应用
 * 协调所有模块的工作
 */

class VoiceAssistant {
    constructor() {
        this.socket = null;
        this.audioManager = null;
        this.uiManager = null;
        
        this.isConnected = false;
        this.isRecording = false;
        this.isProcessing = false;
        this.isPlaying = false;
        
        // 配置
        this.config = {
            serverUrl: window.location.origin,
            reconnectAttempts: 5,
            reconnectDelay: 2000,
            maxRecordingTime: 30000, // 30秒
            silenceThreshold: 1500 // 1.5秒
        };
        
        this.initialize();
    }
    
    async initialize() {
        try {
            console.log('Initializing Voice Assistant...');
            
            // 检查浏览器支持
            if (!this.checkBrowserSupport()) {
                this.showError('您的浏览器不支持语音功能，请使用Chrome、Firefox或Safari');
                return;
            }
            
            // 初始化UI管理器
            this.uiManager = new UIManager();
            this.uiManager.setCallbacks({
                onVoiceButtonPress: () => this.startRecording(),
                onVoiceButtonRelease: () => this.stopRecording(),
                onSettingsChange: (setting, value) => this.handleSettingChange(setting, value),
                onContextClear: () => this.clearContext()
            });
            
            // 初始化音频管理器
            this.audioManager = new AudioManager();
            this.audioManager.setCallbacks({
                onAudioData: (data) => this.handleAudioData(data),
                onRecordingStart: () => this.handleRecordingStart(),
                onRecordingStop: () => this.handleRecordingStop(),
                onError: (error) => this.showError(error)
            });
            
            // 初始化音频可视化
            this.audioManager.initializeVisualizer('visualizerCanvas');
            
            // 连接WebSocket
            await this.connectWebSocket();
            
            console.log('Voice Assistant initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize Voice Assistant:', error);
            this.showError('语音助手初始化失败');
        }
    }
    
    checkBrowserSupport() {
        const support = AudioUtils.checkAudioSupport();
        
        if (!support.mediaRecorder) {
            console.error('MediaRecorder not supported');
            return false;
        }
        
        if (!support.getUserMedia) {
            console.error('getUserMedia not supported');
            return false;
        }
        
        if (!support.webAudio) {
            console.error('Web Audio API not supported');
            return false;
        }
        
        if (typeof io === 'undefined') {
            console.error('Socket.IO not loaded');
            return false;
        }
        
        return true;
    }
    
    async connectWebSocket() {
        return new Promise((resolve, reject) => {
            try {
                this.socket = io(this.config.serverUrl, {
                    transports: ['websocket', 'polling'],
                    timeout: 10000
                });
                
                this.socket.on('connect', () => {
                    console.log('WebSocket connected');
                    this.isConnected = true;
                    this.uiManager.setConnectionStatus(true, '已连接');
                    this.uiManager.setState('idle');
                    resolve();
                });
                
                this.socket.on('disconnect', () => {
                    console.log('WebSocket disconnected');
                    this.isConnected = false;
                    this.uiManager.setConnectionStatus(false, '连接断开');
                    this.uiManager.setState('disabled');
                });
                
                this.socket.on('connect_error', (error) => {
                    console.error('WebSocket connection error:', error);
                    this.uiManager.setConnectionStatus(false, '连接失败');
                    reject(error);
                });
                
                // 注册事件处理器
                this.registerSocketEvents();
                
                // 连接超时
                setTimeout(() => {
                    if (!this.isConnected) {
                        reject(new Error('Connection timeout'));
                    }
                }, 10000);
                
            } catch (error) {
                reject(error);
            }
        });
    }
    
    registerSocketEvents() {
        // 连接确认
        this.socket.on('connected', (data) => {
            console.log('Server connection confirmed:', data);
        });
        
        // 录音开始确认
        this.socket.on('recording_started', (data) => {
            console.log('Recording started on server');
        });
        
        // 录音停止确认
        this.socket.on('recording_stopped', (data) => {
            console.log('Recording stopped on server');
        });
        
        // 自动停止录音
        this.socket.on('auto_stop_recording', (data) => {
            console.log('Auto stop recording:', data.reason);
            this.stopRecording();
        });
        
        // 处理开始
        this.socket.on('processing_started', (data) => {
            console.log('Processing started');
            this.isProcessing = true;
            this.uiManager.setState('processing', '正在识别语音...');
        });
        
        // ASR结果
        this.socket.on('asr_result', (data) => {
            console.log('ASR result:', data.text);
            this.uiManager.setState('processing', `识别结果: ${data.text}`);
        });
        
        // 工具调用结果
        this.socket.on('tool_result', (data) => {
            console.log('Tool result:', data);
            if (data.success) {
                this.uiManager.showToolModal(data.tool_name, data);
            }
        });
        
        // LLM结果
        this.socket.on('llm_result', (data) => {
            console.log('LLM result:', data.text);
            this.uiManager.setState('processing', '正在合成语音...');
        });
        
        // 播放开始
        this.socket.on('playback_started', (data) => {
            console.log('Playback started:', data.text);
            this.isPlaying = true;
            this.uiManager.setState('playing', data.text);
        });
        
        // 播放完成
        this.socket.on('playback_completed', (data) => {
            console.log('Playback completed');
            this.isPlaying = false;
            this.isProcessing = false;
            this.uiManager.setState('idle');
        });
        
        // 播放中断
        this.socket.on('playback_interrupted', (data) => {
            console.log('Playback interrupted');
            this.isPlaying = false;
            this.isProcessing = false;
            this.uiManager.setState('idle');
        });
        
        // 处理完成
        this.socket.on('processing_completed', (data) => {
            console.log('Processing completed');
            this.isProcessing = false;
            this.uiManager.setState('idle');
        });
        
        // 错误处理
        this.socket.on('error', (data) => {
            console.error('Server error:', data.message);
            this.showError(data.message);
            this.resetState();
        });
    }
    
    startRecording() {
        if (!this.isConnected || this.isRecording || this.isProcessing || this.isPlaying) {
            return;
        }
        
        console.log('Starting recording...');
        
        // 发送开始录音信号
        this.socket.emit('start_recording');
        
        // 开始音频录制
        if (this.audioManager.startRecording()) {
            this.isRecording = true;
            this.uiManager.setState('recording');
            
            // 设置最大录音时间
            this.recordingTimeout = setTimeout(() => {
                this.stopRecording();
            }, this.config.maxRecordingTime);
        } else {
            this.showError('无法开始录音，请检查麦克风权限');
        }
    }
    
    stopRecording() {
        if (!this.isRecording) {
            return;
        }
        
        console.log('Stopping recording...');
        
        // 清除录音超时
        if (this.recordingTimeout) {
            clearTimeout(this.recordingTimeout);
            this.recordingTimeout = null;
        }
        
        // 停止音频录制
        this.audioManager.stopRecording();
        this.isRecording = false;
        
        // 发送停止录音信号
        this.socket.emit('stop_recording');
        
        this.uiManager.setState('processing', '正在处理录音...');
    }
    
    handleAudioData(audioBlob) {
        if (!this.isRecording || !this.socket) {
            return;
        }
        
        // 将音频数据转换为ArrayBuffer并发送
        AudioUtils.blobToArrayBuffer(audioBlob).then(arrayBuffer => {
            const base64Data = AudioUtils.arrayBufferToBase64(arrayBuffer);
            this.socket.emit('audio_data', {
                audio: base64Data,
                timestamp: Date.now()
            });
        }).catch(error => {
            console.error('Failed to process audio data:', error);
        });
    }
    
    handleRecordingStart() {
        console.log('Audio recording started');
    }
    
    handleRecordingStop() {
        console.log('Audio recording stopped');
    }
    
    interruptPlayback() {
        if (this.isPlaying) {
            this.socket.emit('interrupt_playback');
            this.audioManager.stopAudio();
        }
    }
    
    handleSettingChange(setting, value) {
        console.log(`Setting changed: ${setting} = ${value}`);
        
        // 这里可以发送设置更改到服务器
        // this.socket.emit('setting_change', { setting, value });
        
        // 本地处理一些设置
        if (setting === 'volume') {
            // 调整音量
        } else if (setting === 'speed') {
            // 调整语速
        }
    }
    
    clearContext() {
        if (this.socket) {
            this.socket.emit('clear_context');
            this.showMessage('对话历史已清空');
        }
    }
    
    resetState() {
        this.isRecording = false;
        this.isProcessing = false;
        this.isPlaying = false;
        
        if (this.recordingTimeout) {
            clearTimeout(this.recordingTimeout);
            this.recordingTimeout = null;
        }
        
        this.audioManager.stopRecording();
        this.audioManager.stopAudio();
        
        if (this.isConnected) {
            this.uiManager.setState('idle');
        } else {
            this.uiManager.setState('disabled');
        }
    }
    
    showError(message) {
        console.error(message);
        this.uiManager.showError(message);
        this.resetState();
    }
    
    showMessage(message) {
        console.log(message);
        // 可以添加消息显示功能
    }
    
    destroy() {
        if (this.socket) {
            this.socket.disconnect();
        }
        
        if (this.audioManager) {
            this.audioManager.destroy();
        }
        
        if (this.recordingTimeout) {
            clearTimeout(this.recordingTimeout);
        }
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.voiceAssistant = new VoiceAssistant();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.voiceAssistant) {
        window.voiceAssistant.destroy();
    }
});
