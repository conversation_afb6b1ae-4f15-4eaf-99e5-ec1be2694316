/**
 * UI管理模块
 * 处理界面交互和状态更新
 */

class UIManager {
    constructor() {
        this.elements = {};
        this.currentState = 'idle';
        this.isInitialized = false;
        
        // 状态映射
        this.stateConfig = {
            idle: {
                buttonClass: '',
                buttonText: '按住说话',
                buttonIcon: '🎤',
                statusText: '准备就绪'
            },
            recording: {
                buttonClass: 'recording',
                buttonText: '正在录音...',
                buttonIcon: '🔴',
                statusText: '正在录音'
            },
            processing: {
                buttonClass: 'processing',
                buttonText: '处理中...',
                buttonIcon: '⚙️',
                statusText: '正在处理'
            },
            playing: {
                buttonClass: 'playing',
                buttonText: '播放中...',
                buttonIcon: '🔊',
                statusText: '正在播放'
            },
            disabled: {
                buttonClass: 'disabled',
                buttonText: '不可用',
                buttonIcon: '❌',
                statusText: '系统不可用'
            }
        };
        
        this.initializeElements();
        this.bindEvents();
    }
    
    initializeElements() {
        // 获取DOM元素
        this.elements = {
            voiceButton: document.getElementById('voiceButton'),
            buttonIcon: document.getElementById('buttonIcon'),
            buttonText: document.getElementById('buttonText'),
            statusText: document.getElementById('statusText'),
            statusDetails: document.getElementById('statusDetails'),
            recordingAnimation: document.getElementById('recordingAnimation'),
            audioVisualizer: document.getElementById('audioVisualizer'),
            settingsButton: document.getElementById('settingsButton'),
            connectionStatus: document.getElementById('connectionStatus'),
            connectionIndicator: document.getElementById('connectionIndicator'),
            connectionText: document.getElementById('connectionText'),
            errorToast: document.getElementById('errorToast'),
            errorContent: document.getElementById('errorContent'),
            
            // 模态框
            toolModal: document.getElementById('toolModal'),
            toolTitle: document.getElementById('toolTitle'),
            toolContent: document.getElementById('toolContent'),
            closeModal: document.getElementById('closeModal'),
            
            settingsModal: document.getElementById('settingsModal'),
            closeSettingsModal: document.getElementById('closeSettingsModal'),
            voiceSelect: document.getElementById('voiceSelect'),
            volumeSlider: document.getElementById('volumeSlider'),
            volumeValue: document.getElementById('volumeValue'),
            speedSlider: document.getElementById('speedSlider'),
            speedValue: document.getElementById('speedValue'),
            toolsEnabled: document.getElementById('toolsEnabled'),
            clearContextButton: document.getElementById('clearContextButton')
        };
        
        // 检查必要元素
        const requiredElements = ['voiceButton', 'buttonIcon', 'buttonText', 'statusText'];
        for (const elementName of requiredElements) {
            if (!this.elements[elementName]) {
                console.error(`Required element not found: ${elementName}`);
                return;
            }
        }
        
        this.isInitialized = true;
        console.log('UI Manager initialized');
    }
    
    bindEvents() {
        if (!this.isInitialized) return;
        
        // 语音按钮事件
        if (this.elements.voiceButton) {
            // 鼠标事件
            this.elements.voiceButton.addEventListener('mousedown', (e) => {
                e.preventDefault();
                this.onButtonPress();
            });
            
            this.elements.voiceButton.addEventListener('mouseup', (e) => {
                e.preventDefault();
                this.onButtonRelease();
            });
            
            this.elements.voiceButton.addEventListener('mouseleave', (e) => {
                e.preventDefault();
                this.onButtonRelease();
            });
            
            // 触摸事件
            this.elements.voiceButton.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.onButtonPress();
            });
            
            this.elements.voiceButton.addEventListener('touchend', (e) => {
                e.preventDefault();
                this.onButtonRelease();
            });
            
            this.elements.voiceButton.addEventListener('touchcancel', (e) => {
                e.preventDefault();
                this.onButtonRelease();
            });
        }
        
        // 设置按钮
        if (this.elements.settingsButton) {
            this.elements.settingsButton.addEventListener('click', () => {
                this.showSettingsModal();
            });
        }
        
        // 模态框关闭事件
        if (this.elements.closeModal) {
            this.elements.closeModal.addEventListener('click', () => {
                this.hideToolModal();
            });
        }
        
        if (this.elements.closeSettingsModal) {
            this.elements.closeSettingsModal.addEventListener('click', () => {
                this.hideSettingsModal();
            });
        }
        
        // 点击模态框外部关闭
        if (this.elements.toolModal) {
            this.elements.toolModal.addEventListener('click', (e) => {
                if (e.target === this.elements.toolModal) {
                    this.hideToolModal();
                }
            });
        }
        
        if (this.elements.settingsModal) {
            this.elements.settingsModal.addEventListener('click', (e) => {
                if (e.target === this.elements.settingsModal) {
                    this.hideSettingsModal();
                }
            });
        }
        
        // 设置控件事件
        this.bindSettingsEvents();
        
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && !e.repeat) {
                e.preventDefault();
                this.onButtonPress();
            }
        });
        
        document.addEventListener('keyup', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                this.onButtonRelease();
            }
        });
    }
    
    bindSettingsEvents() {
        // 音量滑块
        if (this.elements.volumeSlider && this.elements.volumeValue) {
            this.elements.volumeSlider.addEventListener('input', (e) => {
                this.elements.volumeValue.textContent = e.target.value + '%';
                this.onSettingChange('volume', e.target.value);
            });
        }
        
        // 语速滑块
        if (this.elements.speedSlider && this.elements.speedValue) {
            this.elements.speedSlider.addEventListener('input', (e) => {
                this.elements.speedValue.textContent = e.target.value + '%';
                this.onSettingChange('speed', e.target.value);
            });
        }
        
        // 语音选择
        if (this.elements.voiceSelect) {
            this.elements.voiceSelect.addEventListener('change', (e) => {
                this.onSettingChange('voice', e.target.value);
            });
        }
        
        // 工具开关
        if (this.elements.toolsEnabled) {
            this.elements.toolsEnabled.addEventListener('change', (e) => {
                this.onSettingChange('toolsEnabled', e.target.checked);
            });
        }
        
        // 清空上下文按钮
        if (this.elements.clearContextButton) {
            this.elements.clearContextButton.addEventListener('click', () => {
                this.onClearContext();
            });
        }
    }
    
    setState(state, details = '') {
        if (!this.isInitialized || !this.stateConfig[state]) {
            return;
        }
        
        this.currentState = state;
        const config = this.stateConfig[state];
        
        // 更新按钮
        if (this.elements.voiceButton) {
            this.elements.voiceButton.className = `voice-button ${config.buttonClass}`;
        }
        
        if (this.elements.buttonIcon) {
            this.elements.buttonIcon.textContent = config.buttonIcon;
        }
        
        if (this.elements.buttonText) {
            this.elements.buttonText.textContent = config.buttonText;
        }
        
        // 更新状态文本
        if (this.elements.statusText) {
            this.elements.statusText.textContent = config.statusText;
        }
        
        if (this.elements.statusDetails) {
            this.elements.statusDetails.textContent = details;
        }
        
        // 更新可视化器
        if (this.elements.audioVisualizer) {
            if (state === 'recording') {
                this.elements.audioVisualizer.classList.add('active');
            } else {
                this.elements.audioVisualizer.classList.remove('active');
            }
        }
    }
    
    setConnectionStatus(connected, message = '') {
        if (!this.elements.connectionIndicator || !this.elements.connectionText) {
            return;
        }
        
        if (connected) {
            this.elements.connectionIndicator.classList.add('connected');
            this.elements.connectionText.textContent = message || '已连接';
        } else {
            this.elements.connectionIndicator.classList.remove('connected');
            this.elements.connectionText.textContent = message || '连接中...';
        }
    }
    
    showError(message, duration = 5000) {
        if (!this.elements.errorToast || !this.elements.errorContent) {
            console.error(message);
            return;
        }
        
        this.elements.errorContent.textContent = message;
        this.elements.errorToast.classList.add('show');
        
        setTimeout(() => {
            this.elements.errorToast.classList.remove('show');
        }, duration);
    }
    
    showToolModal(toolName, toolData) {
        if (!this.elements.toolModal || !this.elements.toolTitle || !this.elements.toolContent) {
            return;
        }
        
        this.elements.toolTitle.textContent = toolName;
        this.elements.toolContent.innerHTML = this.generateToolContent(toolData);
        this.elements.toolModal.classList.add('show');
    }
    
    hideToolModal() {
        if (this.elements.toolModal) {
            this.elements.toolModal.classList.remove('show');
        }
    }
    
    showSettingsModal() {
        if (this.elements.settingsModal) {
            this.elements.settingsModal.classList.add('show');
        }
    }
    
    hideSettingsModal() {
        if (this.elements.settingsModal) {
            this.elements.settingsModal.classList.remove('show');
        }
    }
    
    generateToolContent(toolData) {
        const toolName = toolData.tool_name || '工具';
        
        if (toolName === '办理身份证') {
            return this.generateIdentityCardContent(toolData);
        } else if (toolName === '查询天气') {
            return this.generateWeatherContent(toolData);
        } else if (toolName === '设置提醒') {
            return this.generateReminderContent(toolData);
        } else if (toolName === '播放音乐') {
            return this.generateMusicContent(toolData);
        } else {
            return `<p>${toolData.response_text || '工具执行完成'}</p>`;
        }
    }
    
    generateIdentityCardContent(data) {
        let html = `<h4>${data.process_type}</h4>`;
        
        if (data.materials) {
            html += '<h5>所需材料：</h5><ul>';
            data.materials.forEach(material => {
                html += `<li>${material}</li>`;
            });
            html += '</ul>';
        }
        
        if (data.process) {
            html += '<h5>办理流程：</h5><ol>';
            data.process.forEach(step => {
                html += `<li>${step}</li>`;
            });
            html += '</ol>';
        }
        
        if (data.tips) {
            html += '<h5>注意事项：</h5><ul>';
            data.tips.forEach(tip => {
                html += `<li>${tip}</li>`;
            });
            html += '</ul>';
        }
        
        return html;
    }
    
    generateWeatherContent(data) {
        let html = `<h4>${data.location}天气</h4>`;
        
        if (data.current) {
            const current = data.current;
            html += `
                <div class="weather-current">
                    <p><strong>当前天气：</strong>${current.weather}</p>
                    <p><strong>温度：</strong>${current.temperature}°C</p>
                    <p><strong>湿度：</strong>${current.humidity}%</p>
                    <p><strong>风速：</strong>${current.wind_speed} km/h</p>
                </div>
            `;
        }
        
        if (data.forecast) {
            html += '<h5>未来天气：</h5>';
            data.forecast.forEach(day => {
                html += `
                    <div class="weather-forecast">
                        <strong>${day.date}</strong> ${day.weather} 
                        ${day.low_temp}°C - ${day.high_temp}°C
                    </div>
                `;
            });
        }
        
        if (data.update_time) {
            html += `<p class="weather-update">更新时间：${data.update_time}</p>`;
        }
        
        return html;
    }
    
    generateReminderContent(data) {
        let html = `<h4>提醒设置</h4>`;
        
        if (data.reminder) {
            const reminder = data.reminder;
            html += `
                <div class="reminder-info">
                    <p><strong>提醒内容：</strong>${reminder.content}</p>
                    <p><strong>提醒时间：</strong>${reminder.time}</p>
                    <p><strong>状态：</strong>${reminder.status === 'active' ? '已激活' : '未激活'}</p>
                </div>
            `;
        }
        
        if (data.total_reminders) {
            html += `<p>当前共有 ${data.total_reminders} 个活跃提醒</p>`;
        }
        
        return html;
    }
    
    generateMusicContent(data) {
        let html = `<h4>音乐播放</h4>`;
        
        if (data.current_song) {
            html += `<p><strong>当前歌曲：</strong>${data.current_song}</p>`;
        }
        
        html += `<p><strong>播放状态：</strong>${data.is_playing ? '播放中' : '已暂停'}</p>`;
        
        if (data.playlist) {
            html += '<h5>播放列表：</h5><ul>';
            data.playlist.forEach(song => {
                html += `<li>${song}</li>`;
            });
            html += '</ul>';
        }
        
        return html;
    }
    
    // 事件回调（由主应用设置）
    onButtonPress() {
        if (this.onVoiceButtonPress) {
            this.onVoiceButtonPress();
        }
    }
    
    onButtonRelease() {
        if (this.onVoiceButtonRelease) {
            this.onVoiceButtonRelease();
        }
    }
    
    onSettingChange(setting, value) {
        if (this.onSettingsChange) {
            this.onSettingsChange(setting, value);
        }
    }
    
    onClearContext() {
        if (this.onContextClear) {
            this.onContextClear();
        }
    }
    
    setCallbacks(callbacks) {
        this.onVoiceButtonPress = callbacks.onVoiceButtonPress;
        this.onVoiceButtonRelease = callbacks.onVoiceButtonRelease;
        this.onSettingsChange = callbacks.onSettingsChange;
        this.onContextClear = callbacks.onContextClear;
    }
}

// 导出到全局
window.UIManager = UIManager;
