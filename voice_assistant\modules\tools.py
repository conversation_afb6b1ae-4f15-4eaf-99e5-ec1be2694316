"""
工具调用框架
包含意图识别和可扩展的工具调用系统
"""
import re
import json
import logging
import datetime
from typing import Dict, List, Optional, Any, Callable, Tuple
from abc import ABC, abstractmethod
import threading
import time

try:
    from ..config import Config
except ImportError:
    # 当直接运行时，使用绝对导入
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from config import Config

logger = logging.getLogger(__name__)

class Tool(ABC):
    """工具基类"""
    
    def __init__(self, name: str, description: str, keywords: List[str]):
        self.name = name
        self.description = description
        self.keywords = keywords
        self.enabled = True
    
    @abstractmethod
    def execute(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行工具
        
        Args:
            user_input: 用户输入
            context: 上下文信息
            
        Returns:
            Dict: 执行结果
        """
        pass
    
    def matches(self, user_input: str) -> float:
        """
        检查是否匹配用户输入
        
        Args:
            user_input: 用户输入
            
        Returns:
            float: 匹配度 (0-1)
        """
        if not self.enabled:
            return 0.0
        
        user_input_lower = user_input.lower()
        match_score = 0.0
        
        # 关键词匹配
        for keyword in self.keywords:
            if keyword in user_input_lower:
                match_score += 0.3
        
        # 语义匹配（简单实现）
        semantic_score = self._semantic_match(user_input_lower)
        match_score += semantic_score
        
        return min(1.0, match_score)
    
    def _semantic_match(self, user_input: str) -> float:
        """语义匹配（子类可重写）"""
        return 0.0

class IdentityCardTool(Tool):
    """身份证办理工具"""
    
    def __init__(self):
        super().__init__(
            name="办理身份证",
            description="身份证办理流程指导",
            keywords=["身份证", "办证", "证件", "户籍", "派出所"]
        )
    
    def execute(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行身份证办理指导"""
        try:
            # 分析用户具体需求
            if "首次" in user_input or "第一次" in user_input:
                process_type = "首次申领"
                materials = [
                    "户口簿原件及复印件",
                    "居民身份证申领登记表",
                    "本人近期免冠彩色照片"
                ]
                process = [
                    "1. 携带材料到户籍所在地派出所",
                    "2. 填写《居民身份证申领登记表》",
                    "3. 现场拍照或提交照片",
                    "4. 缴纳工本费（20元）",
                    "5. 领取《居民身份证领取凭证》",
                    "6. 等待制证（一般15-20个工作日）",
                    "7. 凭领取凭证到派出所领取身份证"
                ]
            elif "补办" in user_input or "丢失" in user_input or "遗失" in user_input:
                process_type = "补领身份证"
                materials = [
                    "户口簿原件及复印件",
                    "居民身份证申领登记表",
                    "本人近期免冠彩色照片",
                    "《居民身份证挂失申报回执》（如已挂失）"
                ]
                process = [
                    "1. 先到派出所办理挂失手续",
                    "2. 携带材料到户籍所在地派出所",
                    "3. 填写《居民身份证申领登记表》",
                    "4. 现场拍照或提交照片",
                    "5. 缴纳工本费（40元）",
                    "6. 领取《居民身份证领取凭证》",
                    "7. 等待制证（一般15-20个工作日）",
                    "8. 凭领取凭证到派出所领取身份证"
                ]
            elif "换证" in user_input or "到期" in user_input or "过期" in user_input:
                process_type = "换领身份证"
                materials = [
                    "原居民身份证",
                    "户口簿原件及复印件",
                    "居民身份证申领登记表",
                    "本人近期免冠彩色照片"
                ]
                process = [
                    "1. 携带材料到户籍所在地派出所",
                    "2. 填写《居民身份证申领登记表》",
                    "3. 现场拍照或提交照片",
                    "4. 缴纳工本费（20元）",
                    "5. 领取《居民身份证领取凭证》",
                    "6. 等待制证（一般15-20个工作日）",
                    "7. 凭领取凭证到派出所领取身份证"
                ]
            else:
                process_type = "身份证办理"
                materials = [
                    "户口簿原件及复印件",
                    "居民身份证申领登记表",
                    "本人近期免冠彩色照片"
                ]
                process = [
                    "1. 确定办理类型（首次申领/补领/换领）",
                    "2. 准备相关材料",
                    "3. 到户籍所在地派出所办理",
                    "4. 填表、拍照、缴费",
                    "5. 等待制证并领取"
                ]
            
            return {
                'success': True,
                'tool_name': self.name,
                'process_type': process_type,
                'materials': materials,
                'process': process,
                'tips': [
                    "办理时间：工作日上午8:30-11:30，下午14:00-17:30",
                    "照片要求：近期免冠彩色照片，白色背景",
                    "工本费：首次申领和换领20元，补领40元",
                    "制证时间：一般15-20个工作日",
                    "可申请加急服务（额外收费）"
                ],
                'response_text': f"为您查询到{process_type}的办理流程，需要准备{len(materials)}项材料，共{len(process)}个步骤。"
            }
            
        except Exception as e:
            logger.error(f"Identity card tool error: {e}")
            return {
                'success': False,
                'error': str(e),
                'response_text': '身份证办理信息查询失败，请稍后再试。'
            }
    
    def _semantic_match(self, user_input: str) -> float:
        """语义匹配"""
        semantic_patterns = [
            r'.*办.*证.*',
            r'.*身份证.*',
            r'.*户籍.*',
            r'.*派出所.*',
            r'.*证件.*'
        ]
        
        for pattern in semantic_patterns:
            if re.search(pattern, user_input):
                return 0.4
        
        return 0.0

class WeatherTool(Tool):
    """天气查询工具"""
    
    def __init__(self):
        super().__init__(
            name="查询天气",
            description="本地天气信息查询",
            keywords=["天气", "气温", "下雨", "晴天", "阴天", "温度", "雨", "雪"]
        )
    
    def execute(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行天气查询"""
        try:
            # 模拟天气数据（实际应用中应该调用天气API）
            import random
            
            weather_conditions = ["晴", "多云", "阴", "小雨", "中雨", "雷阵雨"]
            current_weather = random.choice(weather_conditions)
            current_temp = random.randint(15, 30)
            humidity = random.randint(40, 80)
            wind_speed = random.randint(1, 15)
            
            # 生成未来几天的天气
            forecast = []
            for i in range(1, 4):
                date = datetime.datetime.now() + datetime.timedelta(days=i)
                forecast.append({
                    'date': date.strftime('%m月%d日'),
                    'weather': random.choice(weather_conditions),
                    'high_temp': random.randint(20, 35),
                    'low_temp': random.randint(10, 25)
                })
            
            return {
                'success': True,
                'tool_name': self.name,
                'current': {
                    'weather': current_weather,
                    'temperature': current_temp,
                    'humidity': humidity,
                    'wind_speed': wind_speed
                },
                'forecast': forecast,
                'location': '本地',
                'update_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M'),
                'response_text': f"今天{current_weather}，气温{current_temp}度，湿度{humidity}%，风速{wind_speed}公里每小时。"
            }
            
        except Exception as e:
            logger.error(f"Weather tool error: {e}")
            return {
                'success': False,
                'error': str(e),
                'response_text': '天气信息查询失败，请稍后再试。'
            }
    
    def _semantic_match(self, user_input: str) -> float:
        """语义匹配"""
        semantic_patterns = [
            r'.*今天.*天气.*',
            r'.*明天.*天气.*',
            r'.*气温.*',
            r'.*下雨.*',
            r'.*出门.*'
        ]
        
        for pattern in semantic_patterns:
            if re.search(pattern, user_input):
                return 0.4
        
        return 0.0

class ReminderTool(Tool):
    """提醒设置工具"""
    
    def __init__(self):
        super().__init__(
            name="设置提醒",
            description="创建语音提醒功能",
            keywords=["提醒", "闹钟", "定时", "记住", "提醒我", "别忘了"]
        )
        self.reminders = []  # 存储提醒列表
    
    def execute(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行提醒设置"""
        try:
            # 解析提醒内容和时间
            reminder_info = self._parse_reminder(user_input)
            
            if reminder_info:
                # 添加到提醒列表
                reminder_id = len(self.reminders) + 1
                reminder = {
                    'id': reminder_id,
                    'content': reminder_info['content'],
                    'time': reminder_info['time'],
                    'created_at': datetime.datetime.now(),
                    'status': 'active'
                }
                self.reminders.append(reminder)
                
                return {
                    'success': True,
                    'tool_name': self.name,
                    'reminder': reminder,
                    'total_reminders': len([r for r in self.reminders if r['status'] == 'active']),
                    'response_text': f"已为您设置提醒：{reminder_info['content']}，时间：{reminder_info['time']}。"
                }
            else:
                return {
                    'success': False,
                    'error': 'Unable to parse reminder',
                    'response_text': '无法理解您的提醒需求，请说明具体的提醒内容和时间。'
                }
                
        except Exception as e:
            logger.error(f"Reminder tool error: {e}")
            return {
                'success': False,
                'error': str(e),
                'response_text': '提醒设置失败，请稍后再试。'
            }
    
    def _parse_reminder(self, user_input: str) -> Optional[Dict[str, str]]:
        """解析提醒信息"""
        # 简单的时间和内容解析
        time_patterns = [
            (r'(\d+)点', lambda m: f"{m.group(1)}:00"),
            (r'(\d+)点(\d+)分?', lambda m: f"{m.group(1)}:{m.group(2)}"),
            (r'明天', lambda m: "明天"),
            (r'后天', lambda m: "后天"),
            (r'(\d+)分钟后', lambda m: f"{m.group(1)}分钟后"),
            (r'(\d+)小时后', lambda m: f"{m.group(1)}小时后"),
        ]
        
        time_str = "稍后"
        for pattern, formatter in time_patterns:
            match = re.search(pattern, user_input)
            if match:
                time_str = formatter(match)
                break
        
        # 提取提醒内容
        content_patterns = [
            r'提醒我(.+)',
            r'记住(.+)',
            r'别忘了(.+)',
            r'提醒(.+)',
        ]
        
        content = "重要事项"
        for pattern in content_patterns:
            match = re.search(pattern, user_input)
            if match:
                content = match.group(1).strip()
                break
        
        if content and time_str:
            return {
                'content': content,
                'time': time_str
            }
        
        return None
    
    def _semantic_match(self, user_input: str) -> float:
        """语义匹配"""
        semantic_patterns = [
            r'.*提醒.*',
            r'.*记住.*',
            r'.*别忘.*',
            r'.*闹钟.*',
            r'.*定时.*'
        ]
        
        for pattern in semantic_patterns:
            if re.search(pattern, user_input):
                return 0.4
        
        return 0.0

class MusicTool(Tool):
    """音乐播放工具"""
    
    def __init__(self):
        super().__init__(
            name="播放音乐",
            description="音乐播放控制",
            keywords=["音乐", "歌曲", "播放", "暂停", "下一首", "上一首", "停止"]
        )
        self.is_playing = False
        self.current_song = None
        self.playlist = [
            "春天里", "月亮代表我的心", "茉莉花", "青花瓷", "夜空中最亮的星"
        ]
    
    def execute(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行音乐控制"""
        try:
            action = self._parse_music_action(user_input)
            
            if action == "play":
                if not self.is_playing:
                    import random
                    self.current_song = random.choice(self.playlist)
                    self.is_playing = True
                    response = f"开始播放：{self.current_song}"
                else:
                    response = f"正在播放：{self.current_song}"
            
            elif action == "pause":
                if self.is_playing:
                    self.is_playing = False
                    response = f"已暂停播放：{self.current_song}"
                else:
                    response = "当前没有播放音乐"
            
            elif action == "stop":
                self.is_playing = False
                self.current_song = None
                response = "已停止播放"
            
            elif action == "next":
                import random
                self.current_song = random.choice(self.playlist)
                response = f"切换到下一首：{self.current_song}"
            
            elif action == "previous":
                import random
                self.current_song = random.choice(self.playlist)
                response = f"切换到上一首：{self.current_song}"
            
            else:
                response = "音乐控制指令不明确，请说明要播放、暂停还是停止。"
            
            return {
                'success': True,
                'tool_name': self.name,
                'action': action,
                'current_song': self.current_song,
                'is_playing': self.is_playing,
                'playlist': self.playlist,
                'response_text': response
            }
            
        except Exception as e:
            logger.error(f"Music tool error: {e}")
            return {
                'success': False,
                'error': str(e),
                'response_text': '音乐控制失败，请稍后再试。'
            }
    
    def _parse_music_action(self, user_input: str) -> str:
        """解析音乐操作"""
        if any(word in user_input for word in ["播放", "开始", "放"]):
            return "play"
        elif any(word in user_input for word in ["暂停", "停一下"]):
            return "pause"
        elif any(word in user_input for word in ["停止", "关闭"]):
            return "stop"
        elif any(word in user_input for word in ["下一首", "下一个", "切歌"]):
            return "next"
        elif any(word in user_input for word in ["上一首", "上一个", "前一首"]):
            return "previous"
        else:
            return "play"  # 默认播放
    
    def _semantic_match(self, user_input: str) -> float:
        """语义匹配"""
        semantic_patterns = [
            r'.*播放.*音乐.*',
            r'.*听.*歌.*',
            r'.*音乐.*',
            r'.*歌曲.*'
        ]
        
        for pattern in semantic_patterns:
            if re.search(pattern, user_input):
                return 0.4
        
        return 0.0

class IntentRecognizer:
    """意图识别器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.confidence_threshold = config.TOOLS_CONFIDENCE_THRESHOLD
        
    def recognize_intent(self, user_input: str, tools: List[Tool]) -> Tuple[Optional[Tool], float]:
        """
        识别用户意图
        
        Args:
            user_input: 用户输入
            tools: 可用工具列表
            
        Returns:
            Tuple[Optional[Tool], float]: (匹配的工具, 置信度)
        """
        best_tool = None
        best_score = 0.0
        
        for tool in tools:
            if not tool.enabled:
                continue
                
            score = tool.matches(user_input)
            if score > best_score:
                best_score = score
                best_tool = tool
        
        # 检查是否超过阈值
        if best_score >= self.confidence_threshold:
            return best_tool, best_score
        else:
            return None, best_score

class ToolManager:
    """工具管理器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.tools = {}
        self.intent_recognizer = IntentRecognizer(config)
        
        # 工具执行回调
        self.tool_start_callback = None
        self.tool_end_callback = None
        self.tool_error_callback = None
        
        self._initialize_tools()
    
    def _initialize_tools(self):
        """初始化预置工具"""
        if self.config.TOOLS_ENABLED:
            preset_tools = self.config.PRESET_TOOLS
            
            if preset_tools.get('identity_card', {}).get('enabled', False):
                self.register_tool('identity_card', IdentityCardTool())
            
            if preset_tools.get('weather', {}).get('enabled', False):
                self.register_tool('weather', WeatherTool())
            
            if preset_tools.get('reminder', {}).get('enabled', False):
                self.register_tool('reminder', ReminderTool())
            
            if preset_tools.get('music', {}).get('enabled', False):
                self.register_tool('music', MusicTool())
            
            logger.info(f"Initialized {len(self.tools)} tools")
    
    def register_tool(self, tool_id: str, tool: Tool):
        """注册工具"""
        self.tools[tool_id] = tool
        logger.info(f"Registered tool: {tool_id} - {tool.name}")
    
    def unregister_tool(self, tool_id: str):
        """注销工具"""
        if tool_id in self.tools:
            del self.tools[tool_id]
            logger.info(f"Unregistered tool: {tool_id}")
    
    def process_user_input(self, user_input: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        处理用户输入
        
        Args:
            user_input: 用户输入
            context: 上下文信息
            
        Returns:
            Dict: 处理结果
        """
        try:
            # 意图识别
            tool, confidence = self.intent_recognizer.recognize_intent(
                user_input, list(self.tools.values())
            )
            
            if tool:
                logger.info(f"Tool matched: {tool.name} (confidence: {confidence:.2f})")
                
                # 执行工具
                if self.tool_start_callback:
                    self.tool_start_callback(tool.name, user_input)
                
                result = tool.execute(user_input, context)
                result['confidence'] = confidence
                result['matched_tool'] = tool.name
                
                if self.tool_end_callback:
                    self.tool_end_callback(tool.name, result)
                
                return result
            else:
                return {
                    'success': False,
                    'matched_tool': None,
                    'confidence': confidence,
                    'response_text': '没有找到匹配的工具来处理您的请求。'
                }
                
        except Exception as e:
            logger.error(f"Tool processing error: {e}")
            if self.tool_error_callback:
                self.tool_error_callback(str(e))
            
            return {
                'success': False,
                'error': str(e),
                'response_text': '工具处理出现错误。'
            }
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        return [
            {
                'id': tool_id,
                'name': tool.name,
                'description': tool.description,
                'keywords': tool.keywords,
                'enabled': tool.enabled
            }
            for tool_id, tool in self.tools.items()
        ]
    
    def set_tool_callbacks(self,
                          start_callback: Optional[Callable] = None,
                          end_callback: Optional[Callable] = None,
                          error_callback: Optional[Callable] = None):
        """设置工具回调函数"""
        self.tool_start_callback = start_callback
        self.tool_end_callback = end_callback
        self.tool_error_callback = error_callback

def create_tool_manager(config: Config) -> ToolManager:
    """
    创建工具管理器
    
    Args:
        config: 配置对象
        
    Returns:
        ToolManager: 工具管理器实例
    """
    return ToolManager(config)
