#!/usr/bin/env python3
"""
测试模块导入
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        print("  ├── 测试配置模块...")
        from config import get_config, Config
        config = get_config()
        print(f"  │   ✅ 配置加载成功: {config.__class__.__name__}")
        
        print("  ├── 测试ASR模块...")
        from modules.asr import create_asr_manager
        print("  │   ✅ ASR模块导入成功")
        
        print("  ├── 测试TTS模块...")
        from modules.tts import create_tts_manager
        print("  │   ✅ TTS模块导入成功")
        
        print("  ├── 测试VAD模块...")
        from modules.vad import create_vad_detector
        print("  │   ✅ VAD模块导入成功")
        
        print("  ├── 测试LLM模块...")
        from modules.llm import create_llm_manager
        print("  │   ✅ LLM模块导入成功")
        
        print("  ├── 测试工具模块...")
        from modules.tools import create_tool_manager
        print("  │   ✅ 工具模块导入成功")
        
        print("  └── 测试Flask应用...")
        from app import app, socketio
        print("  │   ✅ Flask应用导入成功")
        
        print("\n✅ 所有模块导入测试通过!")
        return True
        
    except ImportError as e:
        print(f"\n❌ 模块导入失败: {e}")
        print("请检查依赖是否正确安装: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
