/**
 * 音频处理模块
 * 处理音频录制、播放和可视化
 */

class AudioManager {
    constructor() {
        this.mediaRecorder = null;
        this.audioStream = null;
        this.audioContext = null;
        this.analyser = null;
        this.dataArray = null;
        this.isRecording = false;
        this.isPlaying = false;
        
        // 音频配置
        this.sampleRate = 16000;
        this.channels = 1;
        this.chunkSize = 1024;
        
        // 回调函数
        this.onAudioData = null;
        this.onRecordingStart = null;
        this.onRecordingStop = null;
        this.onError = null;
        
        // 可视化相关
        this.visualizerCanvas = null;
        this.visualizerContext = null;
        this.animationId = null;
        
        this.initializeAudio();
    }
    
    async initializeAudio() {
        try {
            // 请求麦克风权限
            this.audioStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: this.sampleRate,
                    channelCount: this.channels,
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });
            
            // 创建音频上下文
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
                sampleRate: this.sampleRate
            });
            
            // 创建分析器
            this.analyser = this.audioContext.createAnalyser();
            this.analyser.fftSize = 256;
            this.analyser.smoothingTimeConstant = 0.8;
            
            const bufferLength = this.analyser.frequencyBinCount;
            this.dataArray = new Uint8Array(bufferLength);
            
            // 连接音频流到分析器
            const source = this.audioContext.createMediaStreamSource(this.audioStream);
            source.connect(this.analyser);
            
            console.log('Audio initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize audio:', error);
            if (this.onError) {
                this.onError('麦克风权限被拒绝或音频设备不可用');
            }
        }
    }
    
    startRecording() {
        if (!this.audioStream || this.isRecording) {
            return false;
        }
        
        try {
            // 创建MediaRecorder
            this.mediaRecorder = new MediaRecorder(this.audioStream, {
                mimeType: 'audio/webm;codecs=opus'
            });
            
            // 设置数据处理
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0 && this.onAudioData) {
                    this.onAudioData(event.data);
                }
            };
            
            this.mediaRecorder.onstart = () => {
                this.isRecording = true;
                if (this.onRecordingStart) {
                    this.onRecordingStart();
                }
                this.startVisualization();
            };
            
            this.mediaRecorder.onstop = () => {
                this.isRecording = false;
                if (this.onRecordingStop) {
                    this.onRecordingStop();
                }
                this.stopVisualization();
            };
            
            this.mediaRecorder.onerror = (event) => {
                console.error('MediaRecorder error:', event.error);
                if (this.onError) {
                    this.onError('录音出现错误');
                }
            };
            
            // 开始录音，每100ms发送一次数据
            this.mediaRecorder.start(100);
            return true;
            
        } catch (error) {
            console.error('Failed to start recording:', error);
            if (this.onError) {
                this.onError('开始录音失败');
            }
            return false;
        }
    }
    
    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            return true;
        }
        return false;
    }
    
    async playAudio(audioData) {
        try {
            this.isPlaying = true;
            
            // 创建音频元素
            const audioPlayer = document.getElementById('audioPlayer');
            if (!audioPlayer) {
                throw new Error('Audio player element not found');
            }
            
            // 创建音频URL
            const audioBlob = new Blob([audioData], { type: 'audio/mp3' });
            const audioUrl = URL.createObjectURL(audioBlob);
            
            // 播放音频
            audioPlayer.src = audioUrl;
            audioPlayer.onended = () => {
                this.isPlaying = false;
                URL.revokeObjectURL(audioUrl);
            };
            
            audioPlayer.onerror = () => {
                this.isPlaying = false;
                URL.revokeObjectURL(audioUrl);
                if (this.onError) {
                    this.onError('音频播放失败');
                }
            };
            
            await audioPlayer.play();
            return true;
            
        } catch (error) {
            console.error('Failed to play audio:', error);
            this.isPlaying = false;
            if (this.onError) {
                this.onError('音频播放失败');
            }
            return false;
        }
    }
    
    stopAudio() {
        const audioPlayer = document.getElementById('audioPlayer');
        if (audioPlayer) {
            audioPlayer.pause();
            audioPlayer.currentTime = 0;
        }
        this.isPlaying = false;
    }
    
    initializeVisualizer(canvasId) {
        this.visualizerCanvas = document.getElementById(canvasId);
        if (this.visualizerCanvas) {
            this.visualizerContext = this.visualizerCanvas.getContext('2d');
        }
    }
    
    startVisualization() {
        if (!this.visualizerCanvas || !this.analyser) {
            return;
        }
        
        const canvas = this.visualizerCanvas;
        const ctx = this.visualizerContext;
        const width = canvas.width;
        const height = canvas.height;
        
        const draw = () => {
            if (!this.isRecording) {
                return;
            }
            
            this.animationId = requestAnimationFrame(draw);
            
            // 获取频域数据
            this.analyser.getByteFrequencyData(this.dataArray);
            
            // 清空画布
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.fillRect(0, 0, width, height);
            
            // 绘制频谱
            const barWidth = width / this.dataArray.length;
            let x = 0;
            
            for (let i = 0; i < this.dataArray.length; i++) {
                const barHeight = (this.dataArray[i] / 255) * height * 0.8;
                
                // 创建渐变色
                const gradient = ctx.createLinearGradient(0, height - barHeight, 0, height);
                gradient.addColorStop(0, `rgba(255, 107, 107, 0.8)`);
                gradient.addColorStop(1, `rgba(255, 107, 107, 0.3)`);
                
                ctx.fillStyle = gradient;
                ctx.fillRect(x, height - barHeight, barWidth - 1, barHeight);
                
                x += barWidth;
            }
        };
        
        draw();
    }
    
    stopVisualization() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
        
        // 清空画布
        if (this.visualizerContext) {
            this.visualizerContext.clearRect(
                0, 0, 
                this.visualizerCanvas.width, 
                this.visualizerCanvas.height
            );
        }
    }
    
    getAudioLevel() {
        if (!this.analyser || !this.dataArray) {
            return 0;
        }
        
        this.analyser.getByteFrequencyData(this.dataArray);
        
        // 计算平均音量
        let sum = 0;
        for (let i = 0; i < this.dataArray.length; i++) {
            sum += this.dataArray[i];
        }
        
        return sum / this.dataArray.length / 255;
    }
    
    setCallbacks(callbacks) {
        this.onAudioData = callbacks.onAudioData;
        this.onRecordingStart = callbacks.onRecordingStart;
        this.onRecordingStop = callbacks.onRecordingStop;
        this.onError = callbacks.onError;
    }
    
    destroy() {
        this.stopRecording();
        this.stopAudio();
        this.stopVisualization();
        
        if (this.audioStream) {
            this.audioStream.getTracks().forEach(track => track.stop());
        }
        
        if (this.audioContext) {
            this.audioContext.close();
        }
    }
}

// 音频工具函数
class AudioUtils {
    static async blobToArrayBuffer(blob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsArrayBuffer(blob);
        });
    }
    
    static arrayBufferToBase64(buffer) {
        const bytes = new Uint8Array(buffer);
        let binary = '';
        for (let i = 0; i < bytes.byteLength; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return btoa(binary);
    }
    
    static base64ToArrayBuffer(base64) {
        const binaryString = atob(base64);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        return bytes.buffer;
    }
    
    static formatDuration(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }
    
    static checkAudioSupport() {
        const support = {
            mediaRecorder: typeof MediaRecorder !== 'undefined',
            audioContext: typeof (window.AudioContext || window.webkitAudioContext) !== 'undefined',
            getUserMedia: navigator.mediaDevices && navigator.mediaDevices.getUserMedia,
            webAudio: typeof window.AudioContext !== 'undefined' || typeof window.webkitAudioContext !== 'undefined'
        };
        
        return support;
    }
}

// 导出到全局
window.AudioManager = AudioManager;
window.AudioUtils = AudioUtils;
